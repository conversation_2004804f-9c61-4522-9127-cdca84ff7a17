{"version": 3, "sources": ["../../swiper/modules/virtual.mjs", "../../swiper/modules/keyboard.mjs", "../../swiper/modules/mousewheel.mjs", "../../swiper/shared/create-element-if-not-defined.mjs", "../../swiper/modules/navigation.mjs", "../../swiper/shared/classes-to-selector.mjs", "../../swiper/modules/pagination.mjs", "../../swiper/modules/scrollbar.mjs", "../../swiper/modules/parallax.mjs", "../../swiper/modules/zoom.mjs", "../../swiper/modules/controller.mjs", "../../swiper/modules/a11y.mjs", "../../swiper/modules/history.mjs", "../../swiper/modules/hash-navigation.mjs", "../../swiper/modules/autoplay.mjs", "../../swiper/modules/thumbs.mjs", "../../swiper/modules/free-mode.mjs", "../../swiper/modules/grid.mjs", "../../swiper/modules/manipulation.mjs", "../../swiper/shared/effect-init.mjs", "../../swiper/shared/effect-target.mjs", "../../swiper/shared/effect-virtual-transition-end.mjs", "../../swiper/modules/effect-fade.mjs", "../../swiper/modules/effect-cube.mjs", "../../swiper/shared/create-shadow.mjs", "../../swiper/modules/effect-flip.mjs", "../../swiper/modules/effect-coverflow.mjs", "../../swiper/modules/effect-creative.mjs", "../../swiper/modules/effect-cards.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { a as setCSSProperty, e as elementChildren, s as setInnerHTML, c as createElement } from '../shared/utils.mjs';\n\nfunction Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        setInnerHTML(tempDOM, slideEl);\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      setInnerHTML(slideEl, slide);\n    }\n    if (params.cache) {\n      swiper.virtual.cache[index] = slideEl;\n    }\n    return slideEl;\n  }\n  function update(force, beforeInit, forceActiveIndex) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop,\n      initialSlide\n    } = swiper.params;\n    if (beforeInit && !isLoop && initialSlide > 0) {\n      return;\n    }\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = typeof forceActiveIndex === 'undefined' ? swiper.activeIndex || 0 : forceActiveIndex;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`)).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`)).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n          // shift cache indexes\n          Object.keys(swiper.virtual.cache).forEach(key => {\n            if (key > slidesIndexes) {\n              swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n              swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n              delete swiper.virtual.cache[key];\n            }\n          });\n        }\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n        // shift cache indexes\n        Object.keys(swiper.virtual.cache).forEach(key => {\n          if (key > slidesIndexes) {\n            swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n            swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n            delete swiper.virtual.cache[key];\n          }\n        });\n      }\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    update(false, true);\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}\n\nexport { Virtual as default };\n", "import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { b as elementParents, d as elementOffset } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Keyboard(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true\n    }\n  });\n  function handle(event) {\n    if (!swiper.enabled) return;\n    const {\n      rtlTranslate: rtl\n    } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (!swiper.allowSlideNext && (swiper.isHorizontal() && isArrowRight || swiper.isVertical() && isArrowDown || isPageDown)) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && (swiper.isHorizontal() && isArrowLeft || swiper.isVertical() && isArrowUp || isPageUp)) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n      return undefined;\n    }\n    if (swiper.params.keyboard.onlyInViewport && (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 && elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0) {\n        return undefined;\n      }\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [[swiperOffset.left, swiperOffset.top], [swiperOffset.left + swiperWidth, swiperOffset.top], [swiperOffset.left, swiperOffset.top + swiperHeight], [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight]];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if ((isPageDown || isArrowRight) && !rtl || (isPageUp || isArrowLeft) && rtl) swiper.slideNext();\n      if ((isPageUp || isArrowLeft) && !rtl || (isPageDown || isArrowRight) && rtl) swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable\n  });\n}\n\nexport { Keyboard as default };\n", "import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { n as nextTick, f as now } from '../shared/utils.mjs';\n\n/* eslint-disable consistent-return */\nfunction Mousewheel(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null,\n      noMousewheelClass: 'swiper-no-mousewheel'\n    }\n  });\n  swiper.mousewheel = {\n    enabled: false\n  };\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (swiper.params.mousewheel.thresholdDelta && newEvent.delta < swiper.params.mousewheel.thresholdDelta) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n    if (swiper.params.mousewheel.thresholdTime && now() - lastScrollTime < swiper.params.mousewheel.thresholdTime) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n\n    // Ignore event if the target or its parents have the swiper-no-mousewheel class\n    if (event.target.closest(`.${swiper.params.mousewheel.noMousewheelClass}`)) return;\n    const params = swiper.params.mousewheel;\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n    const data = normalize(e);\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;else return true;\n    } else {\n      delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n    if (delta === 0) return true;\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop ? true : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n\n      const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (newEvent.direction !== prevEvent.direction || newEvent.delta > prevEvent.delta || newEvent.time > prevEvent.time + 150) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta)\n      };\n      const ignoreWheelEvents = lastEventBeforeSnap && newEvent.time < lastEventBeforeSnap.time + 500 && newEvent.delta <= lastEventBeforeSnap.delta && newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n        if (!wasBeginning && swiper.isBeginning || !wasEnd && swiper.isEnd) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true\n          });\n        }\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n\n          const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (prevEvent && (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (recentWheelEvents.length >= 15 && newEvent.time - firstEvent.time < 500 && firstEvent.delta - newEvent.delta >= 1 && newEvent.delta <= 6) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              if (swiper.destroyed || !swiper.params) return;\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplay.disableOnInteraction) swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (params.releaseOnEdges && (position === swiper.minTranslate() || position === swiper.maxTranslate())) {\n          return true;\n        }\n      }\n    }\n    if (e.preventDefault) e.preventDefault();else e.returnValue = false;\n    return false;\n  }\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable\n  });\n}\n\nexport { Mousewheel as default };\n", "import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n", "import { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction Navigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.querySelector(el) || swiper.hostEl.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      } else if (res && res.length === 1) {\n        res = res[0];\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (swiper.enabled) {\n      update();\n      return;\n    }\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.add(swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    let targetIsButton = prevEl.includes(targetEl) || nextEl.includes(targetEl);\n    if (swiper.isElement && !targetIsButton) {\n      const path = e.path || e.composedPath && e.composedPath();\n      if (path) {\n        targetIsButton = path.find(pathEl => nextEl.includes(pathEl) || prevEl.includes(pathEl));\n      }\n    }\n    if (swiper.params.navigation.hideOnClick && !targetIsButton) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Navigation as default };\n", "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n", "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { m as makeElementsArray, j as classesToTokens, c as createElement, n as nextTick, d as elementOffset } from '../shared/utils.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\n\nfunction Scrollbar(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault && e.cancelable) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n      if (!el.length) return;\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(classesToSelector(swiper.params.scrollbar.dragClass));\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(...classesToTokens(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass));\n    }\n    disableDraggable();\n  }\n  on('changeDirection', () => {\n    if (!swiper.scrollbar || !swiper.scrollbar.el) return;\n    const params = swiper.params.scrollbar;\n    let {\n      el\n    } = swiper.scrollbar;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock changeDirection', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}\n\nexport { Scrollbar as default };\n", "import { e as elementChildren } from '../shared/utils.mjs';\n\nfunction Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const elementsSelector = '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]';\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid,\n      isElement\n    } = swiper;\n    const elements = elementChildren(el, elementsSelector);\n    if (swiper.isElement) {\n      elements.push(...elementChildren(swiper.hostEl, elementsSelector));\n    }\n    elements.forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll(`${elementsSelector}, [data-swiper-parallax-rotate]`).forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function (duration) {\n    if (duration === void 0) {\n      duration = swiper.params.speed;\n    }\n    const {\n      el,\n      hostEl\n    } = swiper;\n    const elements = [...el.querySelectorAll(elementsSelector)];\n    if (swiper.isElement) {\n      elements.push(...hostEl.querySelectorAll(elementsSelector));\n    }\n    elements.forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\n\nexport { Parallax as default };\n", "import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren, b as elementParents, d as elementOffset, k as getTranslate } from '../shared/utils.mjs';\n\nfunction Zoom(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      limitToOriginalSize: false,\n      maxRatio: 3,\n      minRatio: 1,\n      panOnMouseMove: false,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let isPanningWithMouse = false;\n  let mousePanStart = {\n    x: 0,\n    y: 0\n  };\n  const mousePanSensitivity = -3; // Negative to invert pan direction\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getMaxRatio() {\n    const params = swiper.params.zoom;\n    const maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (params.limitToOriginalSize && gesture.imageEl && gesture.imageEl.naturalWidth) {\n      const imageMaxRatio = gesture.imageEl.naturalWidth / gesture.imageEl.offsetWidth;\n      return Math.min(imageMaxRatio, maxRatio);\n    }\n    return maxRatio;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x - window.scrollX) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y - window.scrollY) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.hostEl.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = getMaxRatio();\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  let allowTouchMoveTimeout;\n  function allowTouchMove() {\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = false;\n  }\n  function preventTouchMove() {\n    clearTimeout(allowTouchMoveTimeout);\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = true;\n    allowTouchMoveTimeout = setTimeout(() => {\n      if (swiper.destroyed) return;\n      allowTouchMove();\n    });\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    const isMouseEvent = e.pointerType === 'mouse';\n    const isMousePan = isMouseEvent && swiper.params.zoom.panOnMouseMove;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) {\n      return;\n    }\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) {\n      return;\n    }\n    if (!image.isTouched || !gesture.slideEl) {\n      if (isMousePan) onMouseMove(e);\n      return;\n    }\n    if (isMousePan) {\n      onMouseMove(e);\n      return;\n    }\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    preventTouchMove();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    evCache.length = 0;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function onMouseMove(e) {\n    // Only pan if zoomed in and mouse panning is enabled\n    if (currentScale <= 1 || !gesture.imageWrapEl) return;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const currentTransform = window.getComputedStyle(gesture.imageWrapEl).transform;\n    const matrix = new window.DOMMatrix(currentTransform);\n    if (!isPanningWithMouse) {\n      isPanningWithMouse = true;\n      mousePanStart.x = e.clientX;\n      mousePanStart.y = e.clientY;\n      image.startX = matrix.e;\n      image.startY = matrix.f;\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      return;\n    }\n    const deltaX = (e.clientX - mousePanStart.x) * mousePanSensitivity;\n    const deltaY = (e.clientY - mousePanStart.y) * mousePanSensitivity;\n    const scaledWidth = image.width * currentScale;\n    const scaledHeight = image.height * currentScale;\n    const slideWidth = gesture.slideWidth;\n    const slideHeight = gesture.slideHeight;\n    const minX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n    const maxX = -minX;\n    const minY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n    const maxY = -minY;\n    const newX = Math.max(Math.min(image.startX + deltaX, maxX), minX);\n    const newY = Math.max(Math.min(image.startY + deltaY, maxY), minY);\n    gesture.imageWrapEl.style.transitionDuration = '0ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${newX}px, ${newY}px, 0)`;\n    mousePanStart.x = e.clientX;\n    mousePanStart.y = e.clientY;\n    image.startX = newX;\n    image.startY = newY;\n    image.currentX = newX;\n    image.currentY = newY;\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const prevScale = currentScale;\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n      image.touchesStart.x = undefined;\n      image.touchesStart.y = undefined;\n    }\n    const maxRatio = getMaxRatio();\n    zoom.scale = forceZoomRatio || maxRatio;\n    currentScale = forceZoomRatio || maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      imageHeight = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      if (prevScale > 0 && forceZoomRatio && typeof image.currentX === 'number' && typeof image.currentY === 'number') {\n        translateX = image.currentX * zoom.scale / prevScale;\n        translateY = image.currentY * zoom.scale / prevScale;\n      } else {\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n      }\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    image.currentX = translateX;\n    image.currentY = translateY;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    image.currentX = undefined;\n    image.currentY = undefined;\n    image.touchesStart.x = undefined;\n    image.touchesStart.y = undefined;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n    if (swiper.params.zoom.panOnMouseMove) {\n      mousePanStart = {\n        x: 0,\n        y: 0\n      };\n      if (isPanningWithMouse) {\n        isPanningWithMouse = false;\n        image.startX = 0;\n        image.startY = 0;\n      }\n    }\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd();\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}\n\nexport { Zoom as default };\n", "import { n as nextTick, l as elementTransitionEnd } from '../shared/utils.mjs';\n\n/* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\nfunction Controller(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    controller: {\n      control: undefined,\n      inverse: false,\n      by: 'slide' // or 'container'\n    }\n  });\n\n  swiper.controller = {\n    control: undefined\n  };\n  function LinearSpline(x, y) {\n    const binarySearch = function search() {\n      let maxIndex;\n      let minIndex;\n      let guess;\n      return (array, val) => {\n        minIndex = -1;\n        maxIndex = array.length;\n        while (maxIndex - minIndex > 1) {\n          guess = maxIndex + minIndex >> 1;\n          if (array[guess] <= val) {\n            minIndex = guess;\n          } else {\n            maxIndex = guess;\n          }\n        }\n        return maxIndex;\n      };\n    }();\n    this.x = x;\n    this.y = y;\n    this.lastIndex = x.length - 1;\n    // Given an x value (x2), return the expected y2 value:\n    // (x1,y1) is the known point before given value,\n    // (x3,y3) is the known point after given value.\n    let i1;\n    let i3;\n    this.interpolate = function interpolate(x2) {\n      if (!x2) return 0;\n\n      // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n      i3 = binarySearch(this.x, x2);\n      i1 = i3 - 1;\n\n      // We have our indexes i1 & i3, so we can calculate already:\n      // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n      return (x2 - this.x[i1]) * (this.y[i3] - this.y[i1]) / (this.x[i3] - this.x[i1]) + this.y[i1];\n    };\n    return this;\n  }\n  function getInterpolateFunction(c) {\n    swiper.controller.spline = swiper.params.loop ? new LinearSpline(swiper.slidesGrid, c.slidesGrid) : new LinearSpline(swiper.snapGrid, c.snapGrid);\n  }\n  function setTranslate(_t, byController) {\n    const controlled = swiper.controller.control;\n    let multiplier;\n    let controlledTranslate;\n    const Swiper = swiper.constructor;\n    function setControlledTranslate(c) {\n      if (c.destroyed) return;\n\n      // this will create an Interpolate function based on the snapGrids\n      // x is the Grid of the scrolled scroller and y will be the controlled scroller\n      // it makes sense to create this only once and recall it for the interpolation\n      // the function does a lot of value caching for performance\n      const translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n      if (swiper.params.controller.by === 'slide') {\n        getInterpolateFunction(c);\n        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n        // but it did not work out\n        controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n      }\n      if (!controlledTranslate || swiper.params.controller.by === 'container') {\n        multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n        if (Number.isNaN(multiplier) || !Number.isFinite(multiplier)) {\n          multiplier = 1;\n        }\n        controlledTranslate = (translate - swiper.minTranslate()) * multiplier + c.minTranslate();\n      }\n      if (swiper.params.controller.inverse) {\n        controlledTranslate = c.maxTranslate() - controlledTranslate;\n      }\n      c.updateProgress(controlledTranslate);\n      c.setTranslate(controlledTranslate, swiper);\n      c.updateActiveIndex();\n      c.updateSlidesClasses();\n    }\n    if (Array.isArray(controlled)) {\n      for (let i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTranslate(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTranslate(controlled);\n    }\n  }\n  function setTransition(duration, byController) {\n    const Swiper = swiper.constructor;\n    const controlled = swiper.controller.control;\n    let i;\n    function setControlledTransition(c) {\n      if (c.destroyed) return;\n      c.setTransition(duration, swiper);\n      if (duration !== 0) {\n        c.transitionStart();\n        if (c.params.autoHeight) {\n          nextTick(() => {\n            c.updateAutoHeight();\n          });\n        }\n        elementTransitionEnd(c.wrapperEl, () => {\n          if (!controlled) return;\n          c.transitionEnd();\n        });\n      }\n    }\n    if (Array.isArray(controlled)) {\n      for (i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTransition(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTransition(controlled);\n    }\n  }\n  function removeSpline() {\n    if (!swiper.controller.control) return;\n    if (swiper.controller.spline) {\n      swiper.controller.spline = undefined;\n      delete swiper.controller.spline;\n    }\n  }\n  on('beforeInit', () => {\n    if (typeof window !== 'undefined' && (\n    // eslint-disable-line\n    typeof swiper.params.controller.control === 'string' || swiper.params.controller.control instanceof HTMLElement)) {\n      const controlElements = typeof swiper.params.controller.control === 'string' ? [...document.querySelectorAll(swiper.params.controller.control)] : [swiper.params.controller.control];\n      controlElements.forEach(controlElement => {\n        if (!swiper.controller.control) swiper.controller.control = [];\n        if (controlElement && controlElement.swiper) {\n          swiper.controller.control.push(controlElement.swiper);\n        } else if (controlElement) {\n          const eventName = `${swiper.params.eventsPrefix}init`;\n          const onControllerSwiper = e => {\n            swiper.controller.control.push(e.detail[0]);\n            swiper.update();\n            controlElement.removeEventListener(eventName, onControllerSwiper);\n          };\n          controlElement.addEventListener(eventName, onControllerSwiper);\n        }\n      });\n      return;\n    }\n    swiper.controller.control = swiper.params.controller.control;\n  });\n  on('update', () => {\n    removeSpline();\n  });\n  on('resize', () => {\n    removeSpline();\n  });\n  on('observerUpdate', () => {\n    removeSpline();\n  });\n  on('setTranslate', (_s, translate, byController) => {\n    if (!swiper.controller.control || swiper.controller.control.destroyed) return;\n    swiper.controller.setTranslate(translate, byController);\n  });\n  on('setTransition', (_s, duration, byController) => {\n    if (!swiper.controller.control || swiper.controller.control.destroyed) return;\n    swiper.controller.setTransition(duration, byController);\n  });\n  Object.assign(swiper.controller, {\n    setTranslate,\n    setTransition\n  });\n}\n\nexport { Controller as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElement, i as elementIndex, m as makeElementsArray, s as setInnerHTML } from '../shared/utils.mjs';\n\nfunction A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      containerRole: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n      scrollOnFocus: true\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  let preventFocusHandler;\n  let focusTargetSlideEl;\n  let visibilityChangedTimestamp = new Date().getTime();\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    setInnerHTML(notification, message);\n  }\n  function getRandomNumber(size) {\n    if (size === void 0) {\n      size = 16;\n    }\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {\n      const prevEls = makeElementsArray(swiper.navigation.prevEl);\n      const nextEls = makeElementsArray(swiper.navigation.nextEl);\n      if (nextEls.includes(targetEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          notify(params.lastSlideMessage);\n        } else {\n          notify(params.nextSlideMessage);\n        }\n      }\n      if (prevEls.includes(targetEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          notify(params.firstSlideMessage);\n        } else {\n          notify(params.prevSlideMessage);\n        }\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = e => {\n    if (focusTargetSlideEl && focusTargetSlideEl !== e.target && !focusTargetSlideEl.contains(e.target)) {\n      preventFocusHandler = true;\n    }\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    preventFocusHandler = false;\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const onVisibilityChange = e => {\n    visibilityChangedTimestamp = new Date().getTime();\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked || !swiper.params.a11y.scrollOnFocus) return;\n    if (new Date().getTime() - visibilityChangedTimestamp < 100) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    focusTargetSlideEl = slideEl;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    requestAnimationFrame(() => {\n      if (preventFocusHandler) return;\n      if (swiper.params.loop) {\n        swiper.slideToLoop(parseInt(slideEl.getAttribute('data-swiper-slide-index')), 0);\n      } else {\n        swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n      }\n      preventFocusHandler = false;\n    });\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n    if (params.containerRole) {\n      addElRole(containerEl, params.containerRole);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n    // Tab focus\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('focus', handleFocus, true);\n      swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n      swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n    }\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\n\nexport { A11y as default };\n", "import { a as getWindow } from '../shared/ssr-window.esm.mjs';\n\nfunction History(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    history: {\n      enabled: false,\n      root: '',\n      replaceState: false,\n      key: 'slides',\n      keepQuery: false\n    }\n  });\n  let initialized = false;\n  let paths = {};\n  const slugify = text => {\n    return text.toString().replace(/\\s+/g, '-').replace(/[^\\w-]+/g, '').replace(/--+/g, '-').replace(/^-+/, '').replace(/-+$/, '');\n  };\n  const getPathValues = urlOverride => {\n    const window = getWindow();\n    let location;\n    if (urlOverride) {\n      location = new URL(urlOverride);\n    } else {\n      location = window.location;\n    }\n    const pathArray = location.pathname.slice(1).split('/').filter(part => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return {\n      key,\n      value\n    };\n  };\n  const setHistory = (key, index) => {\n    const window = getWindow();\n    if (!initialized || !swiper.params.history.enabled) return;\n    let location;\n    if (swiper.params.url) {\n      location = new URL(swiper.params.url);\n    } else {\n      location = window.location;\n    }\n    const slide = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${index}\"]`) : swiper.slides[index];\n    let value = slugify(slide.getAttribute('data-history'));\n    if (swiper.params.history.root.length > 0) {\n      let root = swiper.params.history.root;\n      if (root[root.length - 1] === '/') root = root.slice(0, root.length - 1);\n      value = `${root}/${key ? `${key}/` : ''}${value}`;\n    } else if (!location.pathname.includes(key)) {\n      value = `${key ? `${key}/` : ''}${value}`;\n    }\n    if (swiper.params.history.keepQuery) {\n      value += location.search;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({\n        value\n      }, null, value);\n    } else {\n      window.history.pushState({\n        value\n      }, null, value);\n    }\n  };\n  const scrollToSlide = (speed, value, runCallbacks) => {\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides[i];\n        const slideHistory = slugify(slide.getAttribute('data-history'));\n        if (slideHistory === value) {\n          const index = swiper.getSlideIndex(slide);\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  };\n  const setHistoryPopState = () => {\n    paths = getPathValues(swiper.params.url);\n    scrollToSlide(swiper.params.speed, paths.value, false);\n  };\n  const init = () => {\n    const window = getWindow();\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    initialized = true;\n    paths = getPathValues(swiper.params.url);\n    if (!paths.key && !paths.value) {\n      if (!swiper.params.history.replaceState) {\n        window.addEventListener('popstate', setHistoryPopState);\n      }\n      return;\n    }\n    scrollToSlide(0, paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', setHistoryPopState);\n    }\n  };\n  const destroy = () => {\n    const window = getWindow();\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', setHistoryPopState);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.history.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.history.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHistory(swiper.params.history.key, swiper.activeIndex);\n    }\n  });\n}\n\nexport { History as default };\n", "import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren } from '../shared/utils.mjs';\n\nfunction HashNavigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    on\n  } = _ref;\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n      getSlideIndex(_s, hash) {\n        if (swiper.virtual && swiper.params.virtual.enabled) {\n          const slideWithHash = swiper.slides.find(slideEl => slideEl.getAttribute('data-hash') === hash);\n          if (!slideWithHash) return 0;\n          const index = parseInt(slideWithHash.getAttribute('data-swiper-slide-index'), 10);\n          return index;\n        }\n        return swiper.getSlideIndex(elementChildren(swiper.slidesEl, `.${swiper.params.slideClass}[data-hash=\"${hash}\"], swiper-slide[data-hash=\"${hash}\"]`)[0]);\n      }\n    }\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') : '';\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.params.hashNavigation.getSlideIndex(swiper, newHash);\n      if (typeof newIndex === 'undefined' || Number.isNaN(newIndex)) return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') || activeSlideEl.getAttribute('data-history') : '';\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, `#${activeSlideHash}` || '');\n      emit('hashSet');\n    } else {\n      document.location.hash = activeSlideHash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (!swiper.params.hashNavigation.enabled || swiper.params.history && swiper.params.history.enabled) return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      const index = swiper.params.hashNavigation.getSlideIndex(swiper, hash);\n      swiper.slideTo(index || 0, speed, swiper.params.runCallbacksOnInit, true);\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}\n\nexport { HashNavigation as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { o as isObject, e as elementChildren } from '../shared/utils.mjs';\n\nfunction Thumb(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    thumbs: {\n      swiper: null,\n      multipleActiveThumbs: true,\n      autoScrollOffset: 0,\n      slideThumbActiveClass: 'swiper-slide-thumb-active',\n      thumbsContainerClass: 'swiper-thumbs'\n    }\n  });\n  let initialized = false;\n  let swiperCreated = false;\n  swiper.thumbs = {\n    swiper: null\n  };\n  function onThumbClick() {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    const clickedIndex = thumbsSwiper.clickedIndex;\n    const clickedSlide = thumbsSwiper.clickedSlide;\n    if (clickedSlide && clickedSlide.classList.contains(swiper.params.thumbs.slideThumbActiveClass)) return;\n    if (typeof clickedIndex === 'undefined' || clickedIndex === null) return;\n    let slideToIndex;\n    if (thumbsSwiper.params.loop) {\n      slideToIndex = parseInt(thumbsSwiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      slideToIndex = clickedIndex;\n    }\n    if (swiper.params.loop) {\n      swiper.slideToLoop(slideToIndex);\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n  function init() {\n    const {\n      thumbs: thumbsParams\n    } = swiper.params;\n    if (initialized) return false;\n    initialized = true;\n    const SwiperClass = swiper.constructor;\n    if (thumbsParams.swiper instanceof SwiperClass) {\n      if (thumbsParams.swiper.destroyed) {\n        initialized = false;\n        return false;\n      }\n      swiper.thumbs.swiper = thumbsParams.swiper;\n      Object.assign(swiper.thumbs.swiper.originalParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      Object.assign(swiper.thumbs.swiper.params, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      swiper.thumbs.swiper.update();\n    } else if (isObject(thumbsParams.swiper)) {\n      const thumbsSwiperParams = Object.assign({}, thumbsParams.swiper);\n      Object.assign(thumbsSwiperParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false\n      });\n      swiper.thumbs.swiper = new SwiperClass(thumbsSwiperParams);\n      swiperCreated = true;\n    }\n    swiper.thumbs.swiper.el.classList.add(swiper.params.thumbs.thumbsContainerClass);\n    swiper.thumbs.swiper.on('tap', onThumbClick);\n    return true;\n  }\n  function update(initial) {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    const slidesPerView = thumbsSwiper.params.slidesPerView === 'auto' ? thumbsSwiper.slidesPerViewDynamic() : thumbsSwiper.params.slidesPerView;\n\n    // Activate thumbs\n    let thumbsToActivate = 1;\n    const thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n    if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n      thumbsToActivate = swiper.params.slidesPerView;\n    }\n    if (!swiper.params.thumbs.multipleActiveThumbs) {\n      thumbsToActivate = 1;\n    }\n    thumbsToActivate = Math.floor(thumbsToActivate);\n    thumbsSwiper.slides.forEach(slideEl => slideEl.classList.remove(thumbActiveClass));\n    if (thumbsSwiper.params.loop || thumbsSwiper.params.virtual && thumbsSwiper.params.virtual.enabled) {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        elementChildren(thumbsSwiper.slidesEl, `[data-swiper-slide-index=\"${swiper.realIndex + i}\"]`).forEach(slideEl => {\n          slideEl.classList.add(thumbActiveClass);\n        });\n      }\n    } else {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        if (thumbsSwiper.slides[swiper.realIndex + i]) {\n          thumbsSwiper.slides[swiper.realIndex + i].classList.add(thumbActiveClass);\n        }\n      }\n    }\n    const autoScrollOffset = swiper.params.thumbs.autoScrollOffset;\n    const useOffset = autoScrollOffset && !thumbsSwiper.params.loop;\n    if (swiper.realIndex !== thumbsSwiper.realIndex || useOffset) {\n      const currentThumbsIndex = thumbsSwiper.activeIndex;\n      let newThumbsIndex;\n      let direction;\n      if (thumbsSwiper.params.loop) {\n        const newThumbsSlide = thumbsSwiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') === `${swiper.realIndex}`);\n        newThumbsIndex = thumbsSwiper.slides.indexOf(newThumbsSlide);\n        direction = swiper.activeIndex > swiper.previousIndex ? 'next' : 'prev';\n      } else {\n        newThumbsIndex = swiper.realIndex;\n        direction = newThumbsIndex > swiper.previousIndex ? 'next' : 'prev';\n      }\n      if (useOffset) {\n        newThumbsIndex += direction === 'next' ? autoScrollOffset : -1 * autoScrollOffset;\n      }\n      if (thumbsSwiper.visibleSlidesIndexes && thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n        if (thumbsSwiper.params.centeredSlides) {\n          if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n          } else {\n            newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n          }\n        } else if (newThumbsIndex > currentThumbsIndex && thumbsSwiper.params.slidesPerGroup === 1) ;\n        thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n      }\n    }\n  }\n  on('beforeInit', () => {\n    const {\n      thumbs\n    } = swiper.params;\n    if (!thumbs || !thumbs.swiper) return;\n    if (typeof thumbs.swiper === 'string' || thumbs.swiper instanceof HTMLElement) {\n      const document = getDocument();\n      const getThumbsElementAndInit = () => {\n        const thumbsElement = typeof thumbs.swiper === 'string' ? document.querySelector(thumbs.swiper) : thumbs.swiper;\n        if (thumbsElement && thumbsElement.swiper) {\n          thumbs.swiper = thumbsElement.swiper;\n          init();\n          update(true);\n        } else if (thumbsElement) {\n          const eventName = `${swiper.params.eventsPrefix}init`;\n          const onThumbsSwiper = e => {\n            thumbs.swiper = e.detail[0];\n            thumbsElement.removeEventListener(eventName, onThumbsSwiper);\n            init();\n            update(true);\n            thumbs.swiper.update();\n            swiper.update();\n          };\n          thumbsElement.addEventListener(eventName, onThumbsSwiper);\n        }\n        return thumbsElement;\n      };\n      const watchForThumbsToAppear = () => {\n        if (swiper.destroyed) return;\n        const thumbsElement = getThumbsElementAndInit();\n        if (!thumbsElement) {\n          requestAnimationFrame(watchForThumbsToAppear);\n        }\n      };\n      requestAnimationFrame(watchForThumbsToAppear);\n    } else {\n      init();\n      update(true);\n    }\n  });\n  on('slideChange update resize observerUpdate', () => {\n    update();\n  });\n  on('setTransition', (_s, duration) => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    thumbsSwiper.setTransition(duration);\n  });\n  on('beforeDestroy', () => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    if (swiperCreated) {\n      thumbsSwiper.destroy();\n    }\n  });\n  Object.assign(swiper.thumbs, {\n    init,\n    update\n  });\n}\n\nexport { Thumb as default };\n", "import { f as now, l as elementTransitionEnd } from '../shared/utils.mjs';\n\nfunction freeMode(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    once\n  } = _ref;\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02\n    }\n  });\n  function onTouchStart() {\n    if (swiper.params.cssMode) return;\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({\n      currentPos: swiper.rtl ? swiper.translate : -swiper.translate\n    });\n  }\n  function onTouchMove() {\n    if (swiper.params.cssMode) return;\n    const {\n      touchEventsData: data,\n      touches\n    } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now()\n    });\n  }\n  function onTouchEnd(_ref2) {\n    let {\n      currentPos\n    } = _ref2;\n    if (swiper.params.cssMode) return;\n    const {\n      params,\n      wrapperEl,\n      rtlTranslate: rtl,\n      snapGrid,\n      touchEventsData: data\n    } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n        if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      emit('_freeModeStaticRelease');\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd\n    }\n  });\n}\n\nexport { freeMode as default };\n", "function Grid(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  let wasMultiRow;\n  const getSpaceBetween = () => {\n    let spaceBetween = swiper.params.spaceBetween;\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n    } else if (typeof spaceBetween === 'string') {\n      spaceBetween = parseFloat(spaceBetween);\n    }\n    return spaceBetween;\n  };\n  const initSlides = slides => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : slides.length;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n    slidesPerRow = slidesNumberEvenToRows / rows;\n  };\n  const unsetSlides = () => {\n    if (swiper.slides) {\n      swiper.slides.forEach(slide => {\n        if (slide.swiperSlideGridSet) {\n          slide.style.height = '';\n          slide.style[swiper.getDirectionLabel('margin-top')] = '';\n        }\n      });\n    }\n  };\n  const updateSlide = (i, slide, slides) => {\n    const {\n      slidesPerGroup\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : slides.length;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.row = row;\n    slide.column = column;\n    slide.style.height = `calc((100% - ${(rows - 1) * spaceBetween}px) / ${rows})`;\n    slide.style[swiper.getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n    slide.swiperSlideGridSet = true;\n  };\n  const updateWrapperSize = (slideSize, snapGrid) => {\n    const {\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    if (!swiper.params.cssMode) {\n      swiper.wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    }\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  const onInit = () => {\n    wasMultiRow = swiper.params.grid && swiper.params.grid.rows > 1;\n  };\n  const onUpdate = () => {\n    const {\n      params,\n      el\n    } = swiper;\n    const isMultiRow = params.grid && params.grid.rows > 1;\n    if (wasMultiRow && !isMultiRow) {\n      el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n      numFullColumns = 1;\n      swiper.emitContainerClasses();\n    } else if (!wasMultiRow && isMultiRow) {\n      el.classList.add(`${params.containerModifierClass}grid`);\n      if (params.grid.fill === 'column') {\n        el.classList.add(`${params.containerModifierClass}grid-column`);\n      }\n      swiper.emitContainerClasses();\n    }\n    wasMultiRow = isMultiRow;\n  };\n  on('init', onInit);\n  on('update', onUpdate);\n  swiper.grid = {\n    initSlides,\n    unsetSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}\n\nexport { Grid as default };\n", "import { s as setInnerHTML } from '../shared/utils.mjs';\n\nfunction appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.append(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\n\nfunction prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.prepend(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n\nfunction addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n\nfunction Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}\n\nexport { Manipulation as default };\n", "function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate _virtualUpdated', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return;\n      // remove shadows\n      swiper.slides.forEach(slideEl => {\n        slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => shadowEl.remove());\n      });\n      // create new one\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}\n\nexport { effectInit as e };\n", "import { g as getSlideTransformEl } from './utils.mjs';\n\nfunction effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\n\nexport { effectTarget as e };\n", "import { l as elementTransitionEnd } from './utils.mjs';\n\nfunction effectVirtualTransitionEnd(_ref) {\n  let {\n    swiper,\n    duration,\n    transformElements,\n    allSlides\n  } = _ref;\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.find(slideEl => slideEl.shadowRoot && slideEl.shadowRoot === el.parentNode);\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\n\nexport { effectVirtualTransitionEnd as e };\n", "import { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\n\nfunction EffectFade(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectFade as default };\n", "import { e as effectInit } from '../shared/effect-init.mjs';\nimport { c as createElement, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCube(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    }\n  });\n  const createSlideShadows = (slideEl, progress, isHorizontal) => {\n    let shadowBefore = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createElement('div', `swiper-slide-shadow-cube swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}`.split(' '));\n      slideEl.append(shadowBefore);\n    }\n    if (!shadowAfter) {\n      shadowAfter = createElement('div', `swiper-slide-shadow-cube swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}`.split(' '));\n      slideEl.append(shadowAfter);\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // create new ones\n    const isHorizontal = swiper.isHorizontal();\n    swiper.slides.forEach(slideEl => {\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      createSlideShadows(slideEl, progress, isHorizontal);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      wrapperEl,\n      slides,\n      width: swiperWidth,\n      height: swiperHeight,\n      rtlTranslate: rtl,\n      size: swiperSize,\n      browser\n    } = swiper;\n    const r = getRotateFix(swiper);\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let cubeShadowEl;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl = swiper.wrapperEl.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          swiper.wrapperEl.append(cubeShadowEl);\n        }\n        cubeShadowEl.style.height = `${swiperWidth}px`;\n      } else {\n        cubeShadowEl = el.querySelector('.swiper-cube-shadow');\n        if (!cubeShadowEl) {\n          cubeShadowEl = createElement('div', 'swiper-cube-shadow');\n          el.append(cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + round * 4 * swiperSize;\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = 3 * swiperSize + swiperSize * 4 * round;\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n      const transform = `rotateX(${r(isHorizontal ? 0 : -slideAngle)}deg) rotateY(${r(isHorizontal ? slideAngle : 0)}deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = slideIndex * 90 + progress * 90;\n        if (rtl) wrapperRotate = -slideIndex * 90 - progress * 90;\n      }\n      slideEl.style.transform = transform;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress, isHorizontal);\n      }\n    }\n    wrapperEl.style.transformOrigin = `50% 50% -${swiperSize / 2}px`;\n    wrapperEl.style['-webkit-transform-origin'] = `50% 50% -${swiperSize / 2}px`;\n    if (params.shadow) {\n      if (isHorizontal) {\n        cubeShadowEl.style.transform = `translate3d(0px, ${swiperWidth / 2 + params.shadowOffset}px, ${-swiperWidth / 2}px) rotateX(89.99deg) rotateZ(0deg) scale(${params.shadowScale})`;\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n        const multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        cubeShadowEl.style.transform = `scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${swiperHeight / 2 + offset}px, ${-swiperHeight / 2 / scale2}px) rotateX(-89.99deg)`;\n      }\n    }\n    const zFactor = (browser.isSafari || browser.isWebView) && browser.needPerspectiveFix ? -swiperSize / 2 : 0;\n    wrapperEl.style.transform = `translate3d(0px,0,${zFactor}px) rotateX(${r(swiper.isHorizontal() ? 0 : wrapperRotate)}deg) rotateY(${r(swiper.isHorizontal() ? -wrapperRotate : 0)}deg)`;\n    wrapperEl.style.setProperty('--swiper-cube-translate-z', `${zFactor}px`);\n  };\n  const setTransition = duration => {\n    const {\n      el,\n      slides\n    } = swiper;\n    slides.forEach(slideEl => {\n      slideEl.style.transitionDuration = `${duration}ms`;\n      slideEl.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(subEl => {\n        subEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      const shadowEl = el.querySelector('.swiper-cube-shadow');\n      if (shadowEl) shadowEl.style.transitionDuration = `${duration}ms`;\n    }\n  };\n  effectInit({\n    effect: 'cube',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.cubeEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      resistanceRatio: 0,\n      spaceBetween: 0,\n      centeredSlides: false,\n      virtualTranslate: true\n    })\n  });\n}\n\nexport { EffectCube as default };\n", "import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectFlip(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    }\n  });\n  const createSlideShadows = (slideEl, progress) => {\n    let shadowBefore = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n    let shadowAfter = swiper.isHorizontal() ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n    if (!shadowBefore) {\n      shadowBefore = createShadow('flip', slideEl, swiper.isHorizontal() ? 'left' : 'top');\n    }\n    if (!shadowAfter) {\n      shadowAfter = createShadow('flip', slideEl, swiper.isHorizontal() ? 'right' : 'bottom');\n    }\n    if (shadowBefore) shadowBefore.style.opacity = Math.max(-progress, 0);\n    if (shadowAfter) shadowAfter.style.opacity = Math.max(progress, 0);\n  };\n  const recreateShadows = () => {\n    // Set shadows\n    swiper.params.flipEffect;\n    swiper.slides.forEach(slideEl => {\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      createSlideShadows(slideEl, progress);\n    });\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.flipEffect;\n    const rotateFix = getRotateFix(swiper);\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      let progress = slideEl.progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min(slideEl.progress, 1), -1);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n      if (params.slideShadows) {\n        createSlideShadows(slideEl, progress);\n      }\n      const transform = `translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateFix(rotateX)}deg) rotateY(${rotateFix(rotateY)}deg)`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'flip',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    recreateShadows,\n    getEffectParams: () => swiper.params.flipEffect,\n    perspective: () => true,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectFlip as default };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCoverflow(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    const r = getRotateFix(swiper);\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${r(rotateX)}deg) rotateY(${r(rotateY)}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow('coverflow', slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow('coverflow', slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}\n\nexport { EffectCoverflow as default };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCreative(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    creativeEffect: {\n      limitProgress: 1,\n      shadowPerProgress: false,\n      progressMultiplier: 1,\n      perspective: true,\n      prev: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      },\n      next: {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        opacity: 1,\n        scale: 1\n      }\n    }\n  });\n  const getTranslateValue = value => {\n    if (typeof value === 'string') return value;\n    return `${value}px`;\n  };\n  const setTranslate = () => {\n    const {\n      slides,\n      wrapperEl,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.creativeEffect;\n    const {\n      progressMultiplier: multiplier\n    } = params;\n    const isCenteredSlides = swiper.params.centeredSlides;\n    const rotateFix = getRotateFix(swiper);\n    if (isCenteredSlides) {\n      const margin = slidesSizesGrid[0] / 2 - swiper.params.slidesOffsetBefore || 0;\n      wrapperEl.style.transform = `translateX(calc(50% - ${margin}px))`;\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideEl.progress, -params.limitProgress), params.limitProgress);\n      let originalProgress = progress;\n      if (!isCenteredSlides) {\n        originalProgress = Math.min(Math.max(slideEl.originalProgress, -params.limitProgress), params.limitProgress);\n      }\n      const offset = slideEl.swiperSlideOffset;\n      const t = [swiper.params.cssMode ? -offset - swiper.translate : -offset, 0, 0];\n      const r = [0, 0, 0];\n      let custom = false;\n      if (!swiper.isHorizontal()) {\n        t[1] = t[0];\n        t[0] = 0;\n      }\n      let data = {\n        translate: [0, 0, 0],\n        rotate: [0, 0, 0],\n        scale: 1,\n        opacity: 1\n      };\n      if (progress < 0) {\n        data = params.next;\n        custom = true;\n      } else if (progress > 0) {\n        data = params.prev;\n        custom = true;\n      }\n      // set translate\n      t.forEach((value, index) => {\n        t[index] = `calc(${value}px + (${getTranslateValue(data.translate[index])} * ${Math.abs(progress * multiplier)}))`;\n      });\n      // set rotates\n      r.forEach((value, index) => {\n        let val = data.rotate[index] * Math.abs(progress * multiplier);\n        r[index] = val;\n      });\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const translateString = t.join(', ');\n      const rotateString = `rotateX(${rotateFix(r[0])}deg) rotateY(${rotateFix(r[1])}deg) rotateZ(${rotateFix(r[2])}deg)`;\n      const scaleString = originalProgress < 0 ? `scale(${1 + (1 - data.scale) * originalProgress * multiplier})` : `scale(${1 - (1 - data.scale) * originalProgress * multiplier})`;\n      const opacityString = originalProgress < 0 ? 1 + (1 - data.opacity) * originalProgress * multiplier : 1 - (1 - data.opacity) * originalProgress * multiplier;\n      const transform = `translate3d(${translateString}) ${rotateString} ${scaleString}`;\n\n      // Set shadows\n      if (custom && data.shadow || !custom) {\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl && data.shadow) {\n          shadowEl = createShadow('creative', slideEl);\n        }\n        if (shadowEl) {\n          const shadowOpacity = params.shadowPerProgress ? progress * (1 / params.limitProgress) : progress;\n          shadowEl.style.opacity = Math.min(Math.max(Math.abs(shadowOpacity), 0), 1);\n        }\n      }\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n      targetEl.style.opacity = opacityString;\n      if (data.origin) {\n        targetEl.style.transformOrigin = data.origin;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'creative',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => swiper.params.creativeEffect.perspective,\n    overwriteParams: () => ({\n      watchSlidesProgress: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectCreative as default };\n", "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\n\nfunction EffectCards(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = rtl ? -swiper.translate : swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n\n      /* eslint-disable */\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rtl ? -rotate : rotate : 0}deg)\n        scale(${scaleString})\n      `;\n      /* eslint-enable */\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow('cards', slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      _loopSwapReset: false,\n      watchSlidesProgress: true,\n      loopAdditionalSlides: swiper.params.cardsEffect.rotate ? 3 : 2,\n      centeredSlides: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectCards as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,QAAQ,MAAM;AACrB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI;AACJ,QAAMA,YAAW,YAAY;AAC7B,SAAO,UAAU;AAAA,IACf,OAAO,CAAC;AAAA,IACR,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,QAAQ,CAAC;AAAA,IACT,QAAQ;AAAA,IACR,YAAY,CAAC;AAAA,EACf;AACA,QAAM,UAAUA,UAAS,cAAc,KAAK;AAC5C,WAAS,YAAY,OAAO,OAAO;AACjC,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,SAAS,OAAO,QAAQ,MAAM,KAAK,GAAG;AAC/C,aAAO,OAAO,QAAQ,MAAM,KAAK;AAAA,IACnC;AAEA,QAAI;AACJ,QAAI,OAAO,aAAa;AACtB,gBAAU,OAAO,YAAY,KAAK,QAAQ,OAAO,KAAK;AACtD,UAAI,OAAO,YAAY,UAAU;AAC/B,qBAAa,SAAS,OAAO;AAC7B,kBAAU,QAAQ,SAAS,CAAC;AAAA,MAC9B;AAAA,IACF,WAAW,OAAO,WAAW;AAC3B,gBAAU,cAAc,cAAc;AAAA,IACxC,OAAO;AACL,gBAAU,cAAc,OAAO,OAAO,OAAO,UAAU;AAAA,IACzD;AACA,YAAQ,aAAa,2BAA2B,KAAK;AACrD,QAAI,CAAC,OAAO,aAAa;AACvB,mBAAa,SAAS,KAAK;AAAA,IAC7B;AACA,QAAI,OAAO,OAAO;AAChB,aAAO,QAAQ,MAAM,KAAK,IAAI;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AACA,WAAS,OAAO,OAAO,YAAY,kBAAkB;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,IACF,IAAI,OAAO;AACX,QAAI,cAAc,CAAC,UAAU,eAAe,GAAG;AAC7C;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,IAAI;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,IAAI,OAAO;AACX,QAAI,CAAC,OAAO,OAAO,SAAS;AAC1B,aAAO,kBAAkB;AAAA,IAC3B;AACA,UAAM,cAAc,OAAO,qBAAqB,cAAc,OAAO,eAAe,IAAI;AACxF,QAAI;AACJ,QAAI,OAAO,aAAc,cAAa;AAAA,QAAa,cAAa,OAAO,aAAa,IAAI,SAAS;AACjG,QAAI;AACJ,QAAI;AACJ,QAAI,gBAAgB;AAClB,oBAAc,KAAK,MAAM,gBAAgB,CAAC,IAAI,iBAAiB;AAC/D,qBAAe,KAAK,MAAM,gBAAgB,CAAC,IAAI,iBAAiB;AAAA,IAClE,OAAO;AACL,oBAAc,iBAAiB,iBAAiB,KAAK;AACrD,sBAAgB,SAAS,gBAAgB,kBAAkB;AAAA,IAC7D;AACA,QAAI,OAAO,cAAc;AACzB,QAAI,KAAK,cAAc;AACvB,QAAI,CAAC,QAAQ;AACX,aAAO,KAAK,IAAI,MAAM,CAAC;AACvB,WAAK,KAAK,IAAI,IAAI,OAAO,SAAS,CAAC;AAAA,IACrC;AACA,QAAI,UAAU,OAAO,WAAW,IAAI,KAAK,MAAM,OAAO,WAAW,CAAC,KAAK;AACvE,QAAI,UAAU,eAAe,cAAc;AACzC,cAAQ;AACR,UAAI,CAAC,eAAgB,WAAU,OAAO,WAAW,CAAC;AAAA,IACpD,WAAW,UAAU,cAAc,cAAc;AAC/C,aAAO,CAAC;AACR,UAAI,eAAgB,WAAU,OAAO,WAAW,CAAC;AAAA,IACnD;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,OAAO;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC;AACD,aAAS,aAAa;AACpB,aAAO,aAAa;AACpB,aAAO,eAAe;AACtB,aAAO,oBAAoB;AAC3B,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB,QAAQ,eAAe,MAAM,CAAC,OAAO;AACxD,UAAI,OAAO,eAAe,sBAAsB,WAAW,gBAAgB;AACzE,eAAO,OAAO,QAAQ,aAAW;AAC/B,kBAAQ,MAAM,UAAU,IAAI,GAAG,SAAS,KAAK,IAAI,OAAO,sBAAsB,CAAC,CAAC;AAAA,QAClF,CAAC;AAAA,MACH;AACA,aAAO,eAAe;AACtB,WAAK,eAAe;AACpB;AAAA,IACF;AACA,QAAI,OAAO,OAAO,QAAQ,gBAAgB;AACxC,aAAO,OAAO,QAAQ,eAAe,KAAK,QAAQ;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,SAAS,YAAY;AAC3B,gBAAM,iBAAiB,CAAC;AACxB,mBAAS,IAAI,MAAM,KAAK,IAAI,KAAK,GAAG;AAClC,2BAAe,KAAK,OAAO,CAAC,CAAC;AAAA,UAC/B;AACA,iBAAO;AAAA,QACT,EAAE;AAAA,MACJ,CAAC;AACD,UAAI,OAAO,OAAO,QAAQ,sBAAsB;AAC9C,mBAAW;AAAA,MACb,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AACA;AAAA,IACF;AACA,UAAM,iBAAiB,CAAC;AACxB,UAAM,gBAAgB,CAAC;AACvB,UAAM,gBAAgB,WAAS;AAC7B,UAAI,aAAa;AACjB,UAAI,QAAQ,GAAG;AACb,qBAAa,OAAO,SAAS;AAAA,MAC/B,WAAW,cAAc,OAAO,QAAQ;AAEtC,qBAAa,aAAa,OAAO;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AACA,QAAI,OAAO;AACT,aAAO,OAAO,OAAO,QAAM,GAAG,QAAQ,IAAI,OAAO,OAAO,UAAU,gBAAgB,CAAC,EAAE,QAAQ,aAAW;AACtG,gBAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,eAAS,IAAI,cAAc,KAAK,YAAY,KAAK,GAAG;AAClD,YAAI,IAAI,QAAQ,IAAI,IAAI;AACtB,gBAAM,aAAa,cAAc,CAAC;AAClC,iBAAO,OAAO,OAAO,QAAM,GAAG,QAAQ,IAAI,OAAO,OAAO,UAAU,6BAA6B,UAAU,6CAA6C,UAAU,IAAI,CAAC,EAAE,QAAQ,aAAW;AACxL,oBAAQ,OAAO;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,SAAS,CAAC,OAAO,SAAS;AAC3C,UAAM,SAAS,SAAS,OAAO,SAAS,IAAI,OAAO;AACnD,aAAS,IAAI,UAAU,IAAI,QAAQ,KAAK,GAAG;AACzC,UAAI,KAAK,QAAQ,KAAK,IAAI;AACxB,cAAM,aAAa,cAAc,CAAC;AAClC,YAAI,OAAO,eAAe,eAAe,OAAO;AAC9C,wBAAc,KAAK,UAAU;AAAA,QAC/B,OAAO;AACL,cAAI,IAAI,WAAY,eAAc,KAAK,UAAU;AACjD,cAAI,IAAI,aAAc,gBAAe,KAAK,UAAU;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AACA,kBAAc,QAAQ,WAAS;AAC7B,aAAO,SAAS,OAAO,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,IAC1D,CAAC;AACD,QAAI,QAAQ;AACV,eAAS,IAAI,eAAe,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACtD,cAAM,QAAQ,eAAe,CAAC;AAC9B,eAAO,SAAS,QAAQ,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,MAC3D;AAAA,IACF,OAAO;AACL,qBAAe,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnC,qBAAe,QAAQ,WAAS;AAC9B,eAAO,SAAS,QAAQ,YAAY,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,MAC3D,CAAC;AAAA,IACH;AACA,oBAAgB,OAAO,UAAU,6BAA6B,EAAE,QAAQ,aAAW;AACjF,cAAQ,MAAM,UAAU,IAAI,GAAG,SAAS,KAAK,IAAI,OAAO,sBAAsB,CAAC,CAAC;AAAA,IAClF,CAAC;AACD,eAAW;AAAA,EACb;AACA,WAASC,aAAY,QAAQ;AAC3B,QAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAI,OAAO,CAAC,EAAG,QAAO,QAAQ,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MACrD;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,OAAO,KAAK,MAAM;AAAA,IACnC;AACA,WAAO,IAAI;AAAA,EACb;AACA,WAASC,cAAa,QAAQ;AAC5B,UAAM,cAAc,OAAO;AAC3B,QAAI,iBAAiB,cAAc;AACnC,QAAI,oBAAoB;AACxB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAI,OAAO,CAAC,EAAG,QAAO,QAAQ,OAAO,QAAQ,OAAO,CAAC,CAAC;AAAA,MACxD;AACA,uBAAiB,cAAc,OAAO;AACtC,0BAAoB,OAAO;AAAA,IAC7B,OAAO;AACL,aAAO,QAAQ,OAAO,QAAQ,MAAM;AAAA,IACtC;AACA,QAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,YAAM,QAAQ,OAAO,QAAQ;AAC7B,YAAM,WAAW,CAAC;AAClB,aAAO,KAAK,KAAK,EAAE,QAAQ,iBAAe;AACxC,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,gBAAgB,SAAS,aAAa,yBAAyB;AACrE,YAAI,eAAe;AACjB,mBAAS,aAAa,2BAA2B,SAAS,eAAe,EAAE,IAAI,iBAAiB;AAAA,QAClG;AACA,iBAAS,SAAS,aAAa,EAAE,IAAI,iBAAiB,IAAI;AAAA,MAC5D,CAAC;AACD,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,gBAAgB,CAAC;AAAA,EAClC;AACA,WAASC,aAAY,eAAe;AAClC,QAAI,OAAO,kBAAkB,eAAe,kBAAkB,KAAM;AACpE,QAAI,cAAc,OAAO;AACzB,QAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACrD,YAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,iBAAO,OAAO,QAAQ,MAAM,cAAc,CAAC,CAAC;AAE5C,iBAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,QAAQ,SAAO;AAC/C,gBAAI,MAAM,eAAe;AACvB,qBAAO,QAAQ,MAAM,MAAM,CAAC,IAAI,OAAO,QAAQ,MAAM,GAAG;AACxD,qBAAO,QAAQ,MAAM,MAAM,CAAC,EAAE,aAAa,2BAA2B,MAAM,CAAC;AAC7E,qBAAO,OAAO,QAAQ,MAAM,GAAG;AAAA,YACjC;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,QAAQ,OAAO,OAAO,cAAc,CAAC,GAAG,CAAC;AAChD,YAAI,cAAc,CAAC,IAAI,YAAa,gBAAe;AACnD,sBAAc,KAAK,IAAI,aAAa,CAAC;AAAA,MACvC;AAAA,IACF,OAAO;AACL,UAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,eAAO,OAAO,QAAQ,MAAM,aAAa;AAEzC,eAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,QAAQ,SAAO;AAC/C,cAAI,MAAM,eAAe;AACvB,mBAAO,QAAQ,MAAM,MAAM,CAAC,IAAI,OAAO,QAAQ,MAAM,GAAG;AACxD,mBAAO,QAAQ,MAAM,MAAM,CAAC,EAAE,aAAa,2BAA2B,MAAM,CAAC;AAC7E,mBAAO,OAAO,QAAQ,MAAM,GAAG;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,OAAO,OAAO,eAAe,CAAC;AAC7C,UAAI,gBAAgB,YAAa,gBAAe;AAChD,oBAAc,KAAK,IAAI,aAAa,CAAC;AAAA,IACvC;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,aAAa,CAAC;AAAA,EAC/B;AACA,WAASC,mBAAkB;AACzB,WAAO,QAAQ,SAAS,CAAC;AACzB,QAAI,OAAO,OAAO,QAAQ,OAAO;AAC/B,aAAO,QAAQ,QAAQ,CAAC;AAAA,IAC1B;AACA,WAAO,IAAI;AACX,WAAO,QAAQ,GAAG,CAAC;AAAA,EACrB;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,CAAC,OAAO,OAAO,QAAQ,QAAS;AACpC,QAAI;AACJ,QAAI,OAAO,OAAO,aAAa,QAAQ,WAAW,aAAa;AAC7D,YAAM,SAAS,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,OAAO,QAAM,GAAG,QAAQ,IAAI,OAAO,OAAO,UAAU,gBAAgB,CAAC;AAClH,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,QAAQ,SAAS,CAAC,GAAG,MAAM;AAClC,4BAAoB;AACpB,eAAO,QAAQ,CAAC,SAAS,eAAe;AACtC,kBAAQ,aAAa,2BAA2B,UAAU;AAC1D,iBAAO,QAAQ,MAAM,UAAU,IAAI;AACnC,kBAAQ,OAAO;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,CAAC,mBAAmB;AACtB,aAAO,QAAQ,SAAS,OAAO,OAAO,QAAQ;AAAA,IAChD;AACA,WAAO,WAAW,KAAK,GAAG,OAAO,OAAO,sBAAsB,SAAS;AACvE,WAAO,OAAO,sBAAsB;AACpC,WAAO,eAAe,sBAAsB;AAC5C,WAAO,OAAO,IAAI;AAAA,EACpB,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,QAAI,CAAC,OAAO,OAAO,QAAQ,QAAS;AACpC,QAAI,OAAO,OAAO,WAAW,CAAC,OAAO,mBAAmB;AACtD,mBAAa,cAAc;AAC3B,uBAAiB,WAAW,MAAM;AAChC,eAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,sBAAsB,MAAM;AAC7B,QAAI,CAAC,OAAO,OAAO,QAAQ,QAAS;AACpC,QAAI,OAAO,OAAO,SAAS;AACzB,qBAAe,OAAO,WAAW,yBAAyB,GAAG,OAAO,WAAW,IAAI;AAAA,IACrF;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,SAAS;AAAA,IAC5B,aAAAH;AAAA,IACA,cAAAC;AAAA,IACA,aAAAC;AAAA,IACA,iBAAAC;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACtVA,SAAS,SAAS,MAAM;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMC,YAAW,YAAY;AAC7B,QAAMC,UAAS,UAAU;AACzB,SAAO,WAAW;AAAA,IAChB,SAAS;AAAA,EACX;AACA,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,YAAY;AAAA,IACd;AAAA,EACF,CAAC;AACD,WAAS,OAAOC,QAAO;AACrB,QAAI,CAAC,OAAO,QAAS;AACrB,UAAM;AAAA,MACJ,cAAc;AAAA,IAChB,IAAI;AACJ,QAAI,IAAIA;AACR,QAAI,EAAE,cAAe,KAAI,EAAE;AAC3B,UAAM,KAAK,EAAE,WAAW,EAAE;AAC1B,UAAM,aAAa,OAAO,OAAO,SAAS;AAC1C,UAAM,WAAW,cAAc,OAAO;AACtC,UAAM,aAAa,cAAc,OAAO;AACxC,UAAM,cAAc,OAAO;AAC3B,UAAM,eAAe,OAAO;AAC5B,UAAM,YAAY,OAAO;AACzB,UAAM,cAAc,OAAO;AAE3B,QAAI,CAAC,OAAO,mBAAmB,OAAO,aAAa,KAAK,gBAAgB,OAAO,WAAW,KAAK,eAAe,aAAa;AACzH,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,mBAAmB,OAAO,aAAa,KAAK,eAAe,OAAO,WAAW,KAAK,aAAa,WAAW;AACpH,aAAO;AAAA,IACT;AACA,QAAI,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS;AACpD,aAAO;AAAA,IACT;AACA,QAAIF,UAAS,iBAAiBA,UAAS,cAAc,aAAaA,UAAS,cAAc,SAAS,YAAY,MAAM,WAAWA,UAAS,cAAc,SAAS,YAAY,MAAM,aAAa;AAC5L,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,SAAS,mBAAmB,YAAY,cAAc,eAAe,gBAAgB,aAAa,cAAc;AAChI,UAAI,SAAS;AAEb,UAAI,eAAe,OAAO,IAAI,IAAI,OAAO,OAAO,UAAU,gBAAgB,EAAE,SAAS,KAAK,eAAe,OAAO,IAAI,IAAI,OAAO,OAAO,gBAAgB,EAAE,EAAE,WAAW,GAAG;AACtK,eAAO;AAAA,MACT;AACA,YAAM,KAAK,OAAO;AAClB,YAAM,cAAc,GAAG;AACvB,YAAM,eAAe,GAAG;AACxB,YAAM,cAAcC,QAAO;AAC3B,YAAM,eAAeA,QAAO;AAC5B,YAAM,eAAe,cAAc,EAAE;AACrC,UAAI,IAAK,cAAa,QAAQ,GAAG;AACjC,YAAM,cAAc,CAAC,CAAC,aAAa,MAAM,aAAa,GAAG,GAAG,CAAC,aAAa,OAAO,aAAa,aAAa,GAAG,GAAG,CAAC,aAAa,MAAM,aAAa,MAAM,YAAY,GAAG,CAAC,aAAa,OAAO,aAAa,aAAa,MAAM,YAAY,CAAC;AACzO,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK,GAAG;AAC9C,cAAM,QAAQ,YAAY,CAAC;AAC3B,YAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,cAAc;AACzF,cAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAG;AACtC,mBAAS;AAAA,QACX;AAAA,MACF;AACA,UAAI,CAAC,OAAQ,QAAO;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,UAAI,YAAY,cAAc,eAAe,cAAc;AACzD,YAAI,EAAE,eAAgB,GAAE,eAAe;AAAA,YAAO,GAAE,cAAc;AAAA,MAChE;AACA,WAAK,cAAc,iBAAiB,CAAC,QAAQ,YAAY,gBAAgB,IAAK,QAAO,UAAU;AAC/F,WAAK,YAAY,gBAAgB,CAAC,QAAQ,cAAc,iBAAiB,IAAK,QAAO,UAAU;AAAA,IACjG,OAAO;AACL,UAAI,YAAY,cAAc,aAAa,aAAa;AACtD,YAAI,EAAE,eAAgB,GAAE,eAAe;AAAA,YAAO,GAAE,cAAc;AAAA,MAChE;AACA,UAAI,cAAc,YAAa,QAAO,UAAU;AAChD,UAAI,YAAY,UAAW,QAAO,UAAU;AAAA,IAC9C;AACA,SAAK,YAAY,EAAE;AACnB,WAAO;AAAA,EACT;AACA,WAAS,SAAS;AAChB,QAAI,OAAO,SAAS,QAAS;AAC7B,IAAAD,UAAS,iBAAiB,WAAW,MAAM;AAC3C,WAAO,SAAS,UAAU;AAAA,EAC5B;AACA,WAAS,UAAU;AACjB,QAAI,CAAC,OAAO,SAAS,QAAS;AAC9B,IAAAA,UAAS,oBAAoB,WAAW,MAAM;AAC9C,WAAO,SAAS,UAAU;AAAA,EAC5B;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,SAAS,SAAS;AAClC,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,SAAS,SAAS;AAC3B,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,UAAU;AAAA,IAC7B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC9GA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMG,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,YAAY;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,SAAS;AAAA,EACX;AACA,MAAI;AACJ,MAAI,iBAAiB,IAAI;AACzB,MAAI;AACJ,QAAM,oBAAoB,CAAC;AAC3B,WAAS,UAAU,GAAG;AAEpB,UAAM,aAAa;AACnB,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AAGT,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,gBAAgB,GAAG;AACrB,WAAK,CAAC,EAAE,aAAa;AAAA,IACvB;AACA,QAAI,iBAAiB,GAAG;AACtB,WAAK,CAAC,EAAE,cAAc;AAAA,IACxB;AACA,QAAI,iBAAiB,GAAG;AACtB,WAAK,CAAC,EAAE,cAAc;AAAA,IACxB;AAGA,QAAI,UAAU,KAAK,EAAE,SAAS,EAAE,iBAAiB;AAC/C,WAAK;AACL,WAAK;AAAA,IACP;AACA,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,YAAY,GAAG;AACjB,WAAK,EAAE;AAAA,IACT;AACA,QAAI,EAAE,YAAY,CAAC,IAAI;AAErB,WAAK;AACL,WAAK;AAAA,IACP;AACA,SAAK,MAAM,OAAO,EAAE,WAAW;AAC7B,UAAI,EAAE,cAAc,GAAG;AAErB,cAAM;AACN,cAAM;AAAA,MACR,OAAO;AAEL,cAAM;AACN,cAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,MAAM,CAAC,IAAI;AACb,WAAK,KAAK,IAAI,KAAK;AAAA,IACrB;AACA,QAAI,MAAM,CAAC,IAAI;AACb,WAAK,KAAK,IAAI,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,eAAe;AAAA,EACxB;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,eAAe;AAAA,EACxB;AACA,WAAS,cAAc,UAAU;AAC/B,QAAI,OAAO,OAAO,WAAW,kBAAkB,SAAS,QAAQ,OAAO,OAAO,WAAW,gBAAgB;AAEvG,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,WAAW,iBAAiB,IAAI,IAAI,iBAAiB,OAAO,OAAO,WAAW,eAAe;AAE7G,aAAO;AAAA,IACT;AAKA,QAAI,SAAS,SAAS,KAAK,IAAI,IAAI,iBAAiB,IAAI;AAEtD,aAAO;AAAA,IACT;AAaA,QAAI,SAAS,YAAY,GAAG;AAC1B,WAAK,CAAC,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC,OAAO,WAAW;AAC9D,eAAO,UAAU;AACjB,aAAK,UAAU,SAAS,GAAG;AAAA,MAC7B;AAAA,IACF,YAAY,CAAC,OAAO,eAAe,OAAO,OAAO,SAAS,CAAC,OAAO,WAAW;AAC3E,aAAO,UAAU;AACjB,WAAK,UAAU,SAAS,GAAG;AAAA,IAC7B;AAEA,qBAAiB,IAAIA,QAAO,KAAK,EAAE,QAAQ;AAE3C,WAAO;AAAA,EACT;AACA,WAAS,cAAc,UAAU;AAC/B,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,SAAS,YAAY,GAAG;AAC1B,UAAI,OAAO,SAAS,CAAC,OAAO,OAAO,QAAQ,OAAO,gBAAgB;AAEhE,eAAO;AAAA,MACT;AAAA,IACF,WAAW,OAAO,eAAe,CAAC,OAAO,OAAO,QAAQ,OAAO,gBAAgB;AAE7E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,WAAS,OAAOC,QAAO;AACrB,QAAI,IAAIA;AACR,QAAI,sBAAsB;AAC1B,QAAI,CAAC,OAAO,QAAS;AAGrB,QAAIA,OAAM,OAAO,QAAQ,IAAI,OAAO,OAAO,WAAW,iBAAiB,EAAE,EAAG;AAC5E,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,OAAO,SAAS;AACzB,QAAE,eAAe;AAAA,IACnB;AACA,QAAI,WAAW,OAAO;AACtB,QAAI,OAAO,OAAO,WAAW,iBAAiB,aAAa;AACzD,iBAAW,SAAS,cAAc,OAAO,OAAO,WAAW,YAAY;AAAA,IACzE;AACA,UAAM,yBAAyB,YAAY,SAAS,SAAS,EAAE,MAAM;AACrE,QAAI,CAAC,OAAO,gBAAgB,CAAC,0BAA0B,CAAC,OAAO,eAAgB,QAAO;AACtF,QAAI,EAAE,cAAe,KAAI,EAAE;AAC3B,QAAI,QAAQ;AACZ,UAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,UAAM,OAAO,UAAU,CAAC;AACxB,QAAI,OAAO,aAAa;AACtB,UAAI,OAAO,aAAa,GAAG;AACzB,YAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,EAAG,SAAQ,CAAC,KAAK,SAAS;AAAA,YAAe,QAAO;AAAA,MAClG,WAAW,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,EAAG,SAAQ,CAAC,KAAK;AAAA,UAAY,QAAO;AAAA,IAC7F,OAAO;AACL,cAAQ,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,SAAS,YAAY,CAAC,KAAK;AAAA,IAC3F;AACA,QAAI,UAAU,EAAG,QAAO;AACxB,QAAI,OAAO,OAAQ,SAAQ,CAAC;AAG5B,QAAI,YAAY,OAAO,aAAa,IAAI,QAAQ,OAAO;AACvD,QAAI,aAAa,OAAO,aAAa,EAAG,aAAY,OAAO,aAAa;AACxE,QAAI,aAAa,OAAO,aAAa,EAAG,aAAY,OAAO,aAAa;AASxE,0BAAsB,OAAO,OAAO,OAAO,OAAO,EAAE,cAAc,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa;AAC7H,QAAI,uBAAuB,OAAO,OAAO,OAAQ,GAAE,gBAAgB;AACnE,QAAI,CAAC,OAAO,OAAO,YAAY,CAAC,OAAO,OAAO,SAAS,SAAS;AAE9D,YAAM,WAAW;AAAA,QACf,MAAM,IAAI;AAAA,QACV,OAAO,KAAK,IAAI,KAAK;AAAA,QACrB,WAAW,KAAK,KAAK,KAAK;AAAA,QAC1B,KAAKA;AAAA,MACP;AAGA,UAAI,kBAAkB,UAAU,GAAG;AACjC,0BAAkB,MAAM;AAAA,MAC1B;AAEA,YAAM,YAAY,kBAAkB,SAAS,kBAAkB,kBAAkB,SAAS,CAAC,IAAI;AAC/F,wBAAkB,KAAK,QAAQ;AAQ/B,UAAI,WAAW;AACb,YAAI,SAAS,cAAc,UAAU,aAAa,SAAS,QAAQ,UAAU,SAAS,SAAS,OAAO,UAAU,OAAO,KAAK;AAC1H,wBAAc,QAAQ;AAAA,QACxB;AAAA,MACF,OAAO;AACL,sBAAc,QAAQ;AAAA,MACxB;AAIA,UAAI,cAAc,QAAQ,GAAG;AAC3B,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AAOL,YAAM,WAAW;AAAA,QACf,MAAM,IAAI;AAAA,QACV,OAAO,KAAK,IAAI,KAAK;AAAA,QACrB,WAAW,KAAK,KAAK,KAAK;AAAA,MAC5B;AACA,YAAM,oBAAoB,uBAAuB,SAAS,OAAO,oBAAoB,OAAO,OAAO,SAAS,SAAS,oBAAoB,SAAS,SAAS,cAAc,oBAAoB;AAC7L,UAAI,CAAC,mBAAmB;AACtB,8BAAsB;AACtB,YAAI,WAAW,OAAO,aAAa,IAAI,QAAQ,OAAO;AACtD,cAAM,eAAe,OAAO;AAC5B,cAAM,SAAS,OAAO;AACtB,YAAI,YAAY,OAAO,aAAa,EAAG,YAAW,OAAO,aAAa;AACtE,YAAI,YAAY,OAAO,aAAa,EAAG,YAAW,OAAO,aAAa;AACtE,eAAO,cAAc,CAAC;AACtB,eAAO,aAAa,QAAQ;AAC5B,eAAO,eAAe;AACtB,eAAO,kBAAkB;AACzB,eAAO,oBAAoB;AAC3B,YAAI,CAAC,gBAAgB,OAAO,eAAe,CAAC,UAAU,OAAO,OAAO;AAClE,iBAAO,oBAAoB;AAAA,QAC7B;AACA,YAAI,OAAO,OAAO,MAAM;AACtB,iBAAO,QAAQ;AAAA,YACb,WAAW,SAAS,YAAY,IAAI,SAAS;AAAA,YAC7C,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AACA,YAAI,OAAO,OAAO,SAAS,QAAQ;AAYjC,uBAAa,OAAO;AACpB,oBAAU;AACV,cAAI,kBAAkB,UAAU,IAAI;AAClC,8BAAkB,MAAM;AAAA,UAC1B;AAEA,gBAAM,YAAY,kBAAkB,SAAS,kBAAkB,kBAAkB,SAAS,CAAC,IAAI;AAC/F,gBAAM,aAAa,kBAAkB,CAAC;AACtC,4BAAkB,KAAK,QAAQ;AAC/B,cAAI,cAAc,SAAS,QAAQ,UAAU,SAAS,SAAS,cAAc,UAAU,YAAY;AAEjG,8BAAkB,OAAO,CAAC;AAAA,UAC5B,WAAW,kBAAkB,UAAU,MAAM,SAAS,OAAO,WAAW,OAAO,OAAO,WAAW,QAAQ,SAAS,SAAS,KAAK,SAAS,SAAS,GAAG;AAOnJ,kBAAM,kBAAkB,QAAQ,IAAI,MAAM;AAC1C,kCAAsB;AACtB,8BAAkB,OAAO,CAAC;AAC1B,sBAAU,SAAS,MAAM;AACvB,kBAAI,OAAO,aAAa,CAAC,OAAO,OAAQ;AACxC,qBAAO,eAAe,OAAO,OAAO,OAAO,MAAM,QAAW,eAAe;AAAA,YAC7E,GAAG,CAAC;AAAA,UACN;AAEA,cAAI,CAAC,SAAS;AAIZ,sBAAU,SAAS,MAAM;AACvB,kBAAI,OAAO,aAAa,CAAC,OAAO,OAAQ;AACxC,oBAAM,kBAAkB;AACxB,oCAAsB;AACtB,gCAAkB,OAAO,CAAC;AAC1B,qBAAO,eAAe,OAAO,OAAO,OAAO,MAAM,QAAW,eAAe;AAAA,YAC7E,GAAG,GAAG;AAAA,UACR;AAAA,QACF;AAGA,YAAI,CAAC,kBAAmB,MAAK,UAAU,CAAC;AAGxC,YAAI,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,qBAAsB,QAAO,SAAS,KAAK;AAEhG,YAAI,OAAO,mBAAmB,aAAa,OAAO,aAAa,KAAK,aAAa,OAAO,aAAa,IAAI;AACvG,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,eAAgB,GAAE,eAAe;AAAA,QAAO,GAAE,cAAc;AAC9D,WAAO;AAAA,EACT;AACA,WAAS,OAAO,QAAQ;AACtB,QAAI,WAAW,OAAO;AACtB,QAAI,OAAO,OAAO,WAAW,iBAAiB,aAAa;AACzD,iBAAW,SAAS,cAAc,OAAO,OAAO,WAAW,YAAY;AAAA,IACzE;AACA,aAAS,MAAM,EAAE,cAAc,gBAAgB;AAC/C,aAAS,MAAM,EAAE,cAAc,gBAAgB;AAC/C,aAAS,MAAM,EAAE,SAAS,MAAM;AAAA,EAClC;AACA,WAAS,SAAS;AAChB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,oBAAoB,SAAS,MAAM;AACpD,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,QAAS,QAAO;AACtC,WAAO,kBAAkB;AACzB,WAAO,WAAW,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,WAAS,UAAU;AACjB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,iBAAiB,OAAO,MAAM;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,WAAW,QAAS,QAAO;AACvC,WAAO,qBAAqB;AAC5B,WAAO,WAAW,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,CAAC,OAAO,OAAO,WAAW,WAAW,OAAO,OAAO,SAAS;AAC9D,cAAQ;AAAA,IACV;AACA,QAAI,OAAO,OAAO,WAAW,QAAS,QAAO;AAAA,EAC/C,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,QAAS,SAAQ;AAAA,EACzC,CAAC;AACD,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACtYA,SAAS,0BAA0B,QAAQ,gBAAgB,QAAQ,YAAY;AAC7E,MAAI,OAAO,OAAO,gBAAgB;AAChC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAO;AACrC,UAAI,CAAC,OAAO,GAAG,KAAK,OAAO,SAAS,MAAM;AACxC,YAAI,UAAU,gBAAgB,OAAO,IAAI,IAAI,WAAW,GAAG,CAAC,EAAE,EAAE,CAAC;AACjE,YAAI,CAAC,SAAS;AACZ,oBAAU,cAAc,OAAO,WAAW,GAAG,CAAC;AAC9C,kBAAQ,YAAY,WAAW,GAAG;AAClC,iBAAO,GAAG,OAAO,OAAO;AAAA,QAC1B;AACA,eAAO,GAAG,IAAI;AACd,uBAAe,GAAG,IAAI;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;ACfA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,WAAW;AAAA,MACX,yBAAyB;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,WAAS,MAAM,IAAI;AACjB,QAAI;AACJ,QAAI,MAAM,OAAO,OAAO,YAAY,OAAO,WAAW;AACpD,YAAM,OAAO,GAAG,cAAc,EAAE,KAAK,OAAO,OAAO,cAAc,EAAE;AACnE,UAAI,IAAK,QAAO;AAAA,IAClB;AACA,QAAI,IAAI;AACN,UAAI,OAAO,OAAO,SAAU,OAAM,CAAC,GAAG,SAAS,iBAAiB,EAAE,CAAC;AACnE,UAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,YAAY,OAAO,IAAI,SAAS,KAAK,OAAO,GAAG,iBAAiB,EAAE,EAAE,WAAW,GAAG;AACrI,cAAM,OAAO,GAAG,cAAc,EAAE;AAAA,MAClC,WAAW,OAAO,IAAI,WAAW,GAAG;AAClC,cAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF;AACA,QAAI,MAAM,CAAC,IAAK,QAAO;AAEvB,WAAO;AAAA,EACT;AACA,WAAS,SAAS,IAAI,UAAU;AAC9B,UAAM,SAAS,OAAO,OAAO;AAC7B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO;AACT,cAAM,UAAU,WAAW,QAAQ,QAAQ,EAAE,GAAG,OAAO,cAAc,MAAM,GAAG,CAAC;AAC/E,YAAI,MAAM,YAAY,SAAU,OAAM,WAAW;AACjD,YAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,gBAAM,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,SAAS;AAAA,QACtE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,SAAS;AAEhB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,QAAI,OAAO,OAAO,MAAM;AACtB,eAAS,QAAQ,KAAK;AACtB,eAAS,QAAQ,KAAK;AACtB;AAAA,IACF;AACA,aAAS,QAAQ,OAAO,eAAe,CAAC,OAAO,OAAO,MAAM;AAC5D,aAAS,QAAQ,OAAO,SAAS,CAAC,OAAO,OAAO,MAAM;AAAA,EACxD;AACA,WAAS,YAAY,GAAG;AACtB,MAAE,eAAe;AACjB,QAAI,OAAO,eAAe,CAAC,OAAO,OAAO,QAAQ,CAAC,OAAO,OAAO,OAAQ;AACxE,WAAO,UAAU;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,WAAS,YAAY,GAAG;AACtB,MAAE,eAAe;AACjB,QAAI,OAAO,SAAS,CAAC,OAAO,OAAO,QAAQ,CAAC,OAAO,OAAO,OAAQ;AAClE,WAAO,UAAU;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,WAAS,OAAO;AACd,UAAM,SAAS,OAAO,OAAO;AAC7B,WAAO,OAAO,aAAa,0BAA0B,QAAQ,OAAO,eAAe,YAAY,OAAO,OAAO,YAAY;AAAA,MACvH,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,EAAE,OAAO,UAAU,OAAO,QAAS;AACvC,QAAI,SAAS,MAAM,OAAO,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,MAAM;AAChC,WAAO,OAAO,OAAO,YAAY;AAAA,MAC/B;AAAA,MACA;AAAA,IACF,CAAC;AACD,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,aAAa,CAAC,IAAI,QAAQ;AAC9B,UAAI,IAAI;AACN,WAAG,iBAAiB,SAAS,QAAQ,SAAS,cAAc,WAAW;AAAA,MACzE;AACA,UAAI,CAAC,OAAO,WAAW,IAAI;AACzB,WAAG,UAAU,IAAI,GAAG,OAAO,UAAU,MAAM,GAAG,CAAC;AAAA,MACjD;AAAA,IACF;AACA,WAAO,QAAQ,QAAM,WAAW,IAAI,MAAM,CAAC;AAC3C,WAAO,QAAQ,QAAM,WAAW,IAAI,MAAM,CAAC;AAAA,EAC7C;AACA,WAAS,UAAU;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,gBAAgB,CAAC,IAAI,QAAQ;AACjC,SAAG,oBAAoB,SAAS,QAAQ,SAAS,cAAc,WAAW;AAC1E,SAAG,UAAU,OAAO,GAAG,OAAO,OAAO,WAAW,cAAc,MAAM,GAAG,CAAC;AAAA,IAC1E;AACA,WAAO,QAAQ,QAAM,cAAc,IAAI,MAAM,CAAC;AAC9C,WAAO,QAAQ,QAAM,cAAc,IAAI,MAAM,CAAC;AAAA,EAChD;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO;AAE9C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,+BAA+B,MAAM;AACtC,WAAO;AAAA,EACT,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,QAAI,OAAO,SAAS;AAClB,aAAO;AACP;AAAA,IACF;AACA,KAAC,GAAG,QAAQ,GAAG,MAAM,EAAE,OAAO,QAAM,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAM,GAAG,UAAU,IAAI,OAAO,OAAO,WAAW,SAAS,CAAC;AAAA,EAC9G,CAAC;AACD,KAAG,SAAS,CAAC,IAAI,MAAM;AACrB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,UAAM,WAAW,EAAE;AACnB,QAAI,iBAAiB,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,QAAQ;AAC1E,QAAI,OAAO,aAAa,CAAC,gBAAgB;AACvC,YAAM,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa;AACxD,UAAI,MAAM;AACR,yBAAiB,KAAK,KAAK,YAAU,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,CAAC;AAAA,MACzF;AAAA,IACF;AACA,QAAI,OAAO,OAAO,WAAW,eAAe,CAAC,gBAAgB;AAC3D,UAAI,OAAO,cAAc,OAAO,OAAO,cAAc,OAAO,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,YAAY,OAAO,WAAW,GAAG,SAAS,QAAQ,GAAI;AAC3K,UAAI;AACJ,UAAI,OAAO,QAAQ;AACjB,mBAAW,OAAO,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAAA,MAC9E,WAAW,OAAO,QAAQ;AACxB,mBAAW,OAAO,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAAA,MAC9E;AACA,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AACA,OAAC,GAAG,QAAQ,GAAG,MAAM,EAAE,OAAO,QAAM,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAM,GAAG,UAAU,OAAO,OAAO,OAAO,WAAW,WAAW,CAAC;AAAA,IACnH;AAAA,EACF,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC;AACzF,SAAK;AACL,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC;AACtF,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACrMA,SAAS,kBAAkB,SAAS;AAClC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO,IAAI,QAAQ,KAAK,EAAE,QAAQ,gBAAgB,MAAM,EACvD,QAAQ,MAAM,GAAG,CAAC;AACrB;;;ACFA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM;AACZ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,IAAI;AAAA,MACJ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,MAAM;AAAA;AAAA,MAEN,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,uBAAuB,YAAU;AAAA,MACjC,qBAAqB,YAAU;AAAA,MAC/B,aAAa,GAAG,GAAG;AAAA,MACnB,mBAAmB,GAAG,GAAG;AAAA,MACzB,eAAe,GAAG,GAAG;AAAA,MACrB,cAAc,GAAG,GAAG;AAAA,MACpB,YAAY,GAAG,GAAG;AAAA,MAClB,aAAa,GAAG,GAAG;AAAA,MACnB,sBAAsB,GAAG,GAAG;AAAA,MAC5B,0BAA0B,GAAG,GAAG;AAAA,MAChC,gBAAgB,GAAG,GAAG;AAAA,MACtB,WAAW,GAAG,GAAG;AAAA,MACjB,iBAAiB,GAAG,GAAG;AAAA,MACvB,eAAe,GAAG,GAAG;AAAA,MACrB,yBAAyB,GAAG,GAAG;AAAA,IACjC;AAAA,EACF,CAAC;AACD,SAAO,aAAa;AAAA,IAClB,IAAI;AAAA,IACJ,SAAS,CAAC;AAAA,EACZ;AACA,MAAI;AACJ,MAAI,qBAAqB;AACzB,WAAS,uBAAuB;AAC9B,WAAO,CAAC,OAAO,OAAO,WAAW,MAAM,CAAC,OAAO,WAAW,MAAM,MAAM,QAAQ,OAAO,WAAW,EAAE,KAAK,OAAO,WAAW,GAAG,WAAW;AAAA,EACzI;AACA,WAAS,eAAe,UAAU,UAAU;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,QAAI,CAAC,SAAU;AACf,eAAW,SAAS,GAAG,aAAa,SAAS,aAAa,MAAM,gBAAgB;AAChF,QAAI,UAAU;AACZ,eAAS,UAAU,IAAI,GAAG,iBAAiB,IAAI,QAAQ,EAAE;AACzD,iBAAW,SAAS,GAAG,aAAa,SAAS,aAAa,MAAM,gBAAgB;AAChF,UAAI,UAAU;AACZ,iBAAS,UAAU,IAAI,GAAG,iBAAiB,IAAI,QAAQ,IAAI,QAAQ,EAAE;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AACA,WAAS,iBAAiB,WAAW,WAAW,QAAQ;AACtD,gBAAY,YAAY;AACxB,gBAAY,YAAY;AACxB,QAAI,cAAc,YAAY,GAAG;AAC/B,aAAO;AAAA,IACT,WAAW,cAAc,YAAY,GAAG;AACtC,aAAO;AAAA,IACT;AACA;AAAA,EACF;AACA,WAAS,cAAc,GAAG;AACxB,UAAM,WAAW,EAAE,OAAO,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC;AACzF,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,MAAE,eAAe;AACjB,UAAM,QAAQ,aAAa,QAAQ,IAAI,OAAO,OAAO;AACrD,QAAI,OAAO,OAAO,MAAM;AACtB,UAAI,OAAO,cAAc,MAAO;AAChC,YAAM,gBAAgB,iBAAiB,OAAO,WAAW,OAAO,OAAO,OAAO,MAAM;AACpF,UAAI,kBAAkB,QAAQ;AAC5B,eAAO,UAAU;AAAA,MACnB,WAAW,kBAAkB,YAAY;AACvC,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,eAAO,YAAY,KAAK;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,KAAK;AAAA,IACtB;AAAA,EACF;AACA,WAAS,SAAS;AAEhB,UAAM,MAAM,OAAO;AACnB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB,EAAG;AAC5B,QAAI,KAAK,OAAO,WAAW;AAC3B,SAAK,kBAAkB,EAAE;AAEzB,QAAI;AACJ,QAAI;AACJ,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO;AACpH,UAAM,QAAQ,OAAO,OAAO,OAAO,KAAK,KAAK,eAAe,OAAO,OAAO,cAAc,IAAI,OAAO,SAAS;AAC5G,QAAI,OAAO,OAAO,MAAM;AACtB,sBAAgB,OAAO,qBAAqB;AAC5C,gBAAU,OAAO,OAAO,iBAAiB,IAAI,KAAK,MAAM,OAAO,YAAY,OAAO,OAAO,cAAc,IAAI,OAAO;AAAA,IACpH,WAAW,OAAO,OAAO,cAAc,aAAa;AAClD,gBAAU,OAAO;AACjB,sBAAgB,OAAO;AAAA,IACzB,OAAO;AACL,sBAAgB,OAAO,iBAAiB;AACxC,gBAAU,OAAO,eAAe;AAAA,IAClC;AAEA,QAAI,OAAO,SAAS,aAAa,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ,SAAS,GAAG;AAClG,YAAM,UAAU,OAAO,WAAW;AAClC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,gBAAgB;AACzB,qBAAa,iBAAiB,QAAQ,CAAC,GAAG,OAAO,aAAa,IAAI,UAAU,UAAU,IAAI;AAC1F,WAAG,QAAQ,WAAS;AAClB,gBAAM,MAAM,OAAO,aAAa,IAAI,UAAU,QAAQ,IAAI,GAAG,cAAc,OAAO,qBAAqB,EAAE;AAAA,QAC3G,CAAC;AACD,YAAI,OAAO,qBAAqB,KAAK,kBAAkB,QAAW;AAChE,gCAAsB,WAAW,iBAAiB;AAClD,cAAI,qBAAqB,OAAO,qBAAqB,GAAG;AACtD,iCAAqB,OAAO,qBAAqB;AAAA,UACnD,WAAW,qBAAqB,GAAG;AACjC,iCAAqB;AAAA,UACvB;AAAA,QACF;AACA,qBAAa,KAAK,IAAI,UAAU,oBAAoB,CAAC;AACrD,oBAAY,cAAc,KAAK,IAAI,QAAQ,QAAQ,OAAO,kBAAkB,IAAI;AAChF,oBAAY,YAAY,cAAc;AAAA,MACxC;AACA,cAAQ,QAAQ,cAAY;AAC1B,cAAM,kBAAkB,CAAC,GAAG,CAAC,IAAI,SAAS,cAAc,SAAS,cAAc,OAAO,EAAE,IAAI,YAAU,GAAG,OAAO,iBAAiB,GAAG,MAAM,EAAE,CAAC,EAAE,IAAI,OAAK,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK;AAC1N,iBAAS,UAAU,OAAO,GAAG,eAAe;AAAA,MAC9C,CAAC;AACD,UAAI,GAAG,SAAS,GAAG;AACjB,gBAAQ,QAAQ,YAAU;AACxB,gBAAM,cAAc,aAAa,MAAM;AACvC,cAAI,gBAAgB,SAAS;AAC3B,mBAAO,UAAU,IAAI,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAAA,UAC7D,WAAW,OAAO,WAAW;AAC3B,mBAAO,aAAa,QAAQ,QAAQ;AAAA,UACtC;AACA,cAAI,OAAO,gBAAgB;AACzB,gBAAI,eAAe,cAAc,eAAe,WAAW;AACzD,qBAAO,UAAU,IAAI,GAAG,GAAG,OAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC;AAAA,YACvE;AACA,gBAAI,gBAAgB,YAAY;AAC9B,6BAAe,QAAQ,MAAM;AAAA,YAC/B;AACA,gBAAI,gBAAgB,WAAW;AAC7B,6BAAe,QAAQ,MAAM;AAAA,YAC/B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,SAAS,QAAQ,OAAO;AAC9B,YAAI,QAAQ;AACV,iBAAO,UAAU,IAAI,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAAA,QAC7D;AACA,YAAI,OAAO,WAAW;AACpB,kBAAQ,QAAQ,CAAC,UAAU,gBAAgB;AACzC,qBAAS,aAAa,QAAQ,gBAAgB,UAAU,kBAAkB,QAAQ;AAAA,UACpF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,gBAAgB;AACzB,gBAAM,uBAAuB,QAAQ,UAAU;AAC/C,gBAAM,sBAAsB,QAAQ,SAAS;AAC7C,mBAAS,IAAI,YAAY,KAAK,WAAW,KAAK,GAAG;AAC/C,gBAAI,QAAQ,CAAC,GAAG;AACd,sBAAQ,CAAC,EAAE,UAAU,IAAI,GAAG,GAAG,OAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC;AAAA,YAC3E;AAAA,UACF;AACA,yBAAe,sBAAsB,MAAM;AAC3C,yBAAe,qBAAqB,MAAM;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,OAAO,gBAAgB;AACzB,cAAM,uBAAuB,KAAK,IAAI,QAAQ,QAAQ,OAAO,qBAAqB,CAAC;AACnF,cAAM,iBAAiB,aAAa,uBAAuB,cAAc,IAAI,WAAW;AACxF,cAAM,aAAa,MAAM,UAAU;AACnC,gBAAQ,QAAQ,YAAU;AACxB,iBAAO,MAAM,OAAO,aAAa,IAAI,aAAa,KAAK,IAAI,GAAG,aAAa;AAAA,QAC7E,CAAC;AAAA,MACH;AAAA,IACF;AACA,OAAG,QAAQ,CAAC,OAAO,eAAe;AAChC,UAAI,OAAO,SAAS,YAAY;AAC9B,cAAM,iBAAiB,kBAAkB,OAAO,YAAY,CAAC,EAAE,QAAQ,gBAAc;AACnF,qBAAW,cAAc,OAAO,sBAAsB,UAAU,CAAC;AAAA,QACnE,CAAC;AACD,cAAM,iBAAiB,kBAAkB,OAAO,UAAU,CAAC,EAAE,QAAQ,aAAW;AAC9E,kBAAQ,cAAc,OAAO,oBAAoB,KAAK;AAAA,QACxD,CAAC;AAAA,MACH;AACA,UAAI,OAAO,SAAS,eAAe;AACjC,YAAI;AACJ,YAAI,OAAO,qBAAqB;AAC9B,iCAAuB,OAAO,aAAa,IAAI,aAAa;AAAA,QAC9D,OAAO;AACL,iCAAuB,OAAO,aAAa,IAAI,eAAe;AAAA,QAChE;AACA,cAAM,SAAS,UAAU,KAAK;AAC9B,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,yBAAyB,cAAc;AACzC,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS;AAAA,QACX;AACA,cAAM,iBAAiB,kBAAkB,OAAO,oBAAoB,CAAC,EAAE,QAAQ,gBAAc;AAC3F,qBAAW,MAAM,YAAY,6BAA6B,MAAM,YAAY,MAAM;AAClF,qBAAW,MAAM,qBAAqB,GAAG,OAAO,OAAO,KAAK;AAAA,QAC9D,CAAC;AAAA,MACH;AACA,UAAI,OAAO,SAAS,YAAY,OAAO,cAAc;AACnD,qBAAa,OAAO,OAAO,aAAa,QAAQ,UAAU,GAAG,KAAK,CAAC;AACnE,YAAI,eAAe,EAAG,MAAK,oBAAoB,KAAK;AAAA,MACtD,OAAO;AACL,YAAI,eAAe,EAAG,MAAK,oBAAoB,KAAK;AACpD,aAAK,oBAAoB,KAAK;AAAA,MAChC;AACA,UAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,cAAM,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,SAAS;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,SAAS;AAEhB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB,EAAG;AAC5B,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO,QAAQ,OAAO,OAAO,KAAK,OAAO,IAAI,OAAO,OAAO,SAAS,KAAK,KAAK,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO;AAC7N,QAAI,KAAK,OAAO,WAAW;AAC3B,SAAK,kBAAkB,EAAE;AACzB,QAAI,iBAAiB;AACrB,QAAI,OAAO,SAAS,WAAW;AAC7B,UAAI,kBAAkB,OAAO,OAAO,OAAO,KAAK,KAAK,eAAe,OAAO,OAAO,cAAc,IAAI,OAAO,SAAS;AACpH,UAAI,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,WAAW,kBAAkB,cAAc;AAC9F,0BAAkB;AAAA,MACpB;AACA,eAAS,IAAI,GAAG,IAAI,iBAAiB,KAAK,GAAG;AAC3C,YAAI,OAAO,cAAc;AACvB,4BAAkB,OAAO,aAAa,KAAK,QAAQ,GAAG,OAAO,WAAW;AAAA,QAC1E,OAAO;AAEL,4BAAkB,IAAI,OAAO,aAAa,IAAI,OAAO,YAAY,kBAAkB,EAAE,WAAW,OAAO,WAAW,OAAO,OAAO,aAAa;AAAA,QAC/I;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,UAAI,OAAO,gBAAgB;AACzB,yBAAiB,OAAO,eAAe,KAAK,QAAQ,OAAO,cAAc,OAAO,UAAU;AAAA,MAC5F,OAAO;AACL,yBAAiB,gBAAgB,OAAO,YAAY,4BAAsC,OAAO,UAAU;AAAA,MAC7G;AAAA,IACF;AACA,QAAI,OAAO,SAAS,eAAe;AACjC,UAAI,OAAO,mBAAmB;AAC5B,yBAAiB,OAAO,kBAAkB,KAAK,QAAQ,OAAO,oBAAoB;AAAA,MACpF,OAAO;AACL,yBAAiB,gBAAgB,OAAO,oBAAoB;AAAA,MAC9D;AAAA,IACF;AACA,WAAO,WAAW,UAAU,CAAC;AAC7B,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO,SAAS,UAAU;AAC5B,qBAAa,OAAO,kBAAkB,EAAE;AAAA,MAC1C;AACA,UAAI,OAAO,SAAS,WAAW;AAC7B,eAAO,WAAW,QAAQ,KAAK,GAAG,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,CAAC,CAAC;AAAA,MACjG;AAAA,IACF,CAAC;AACD,QAAI,OAAO,SAAS,UAAU;AAC5B,WAAK,oBAAoB,GAAG,CAAC,CAAC;AAAA,IAChC;AAAA,EACF;AACA,WAAS,OAAO;AACd,WAAO,OAAO,aAAa,0BAA0B,QAAQ,OAAO,eAAe,YAAY,OAAO,OAAO,YAAY;AAAA,MACvH,IAAI;AAAA,IACN,CAAC;AACD,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,OAAO,GAAI;AAChB,QAAI;AACJ,QAAI,OAAO,OAAO,OAAO,YAAY,OAAO,WAAW;AACrD,WAAK,OAAO,GAAG,cAAc,OAAO,EAAE;AAAA,IACxC;AACA,QAAI,CAAC,MAAM,OAAO,OAAO,OAAO,UAAU;AACxC,WAAK,CAAC,GAAG,SAAS,iBAAiB,OAAO,EAAE,CAAC;AAAA,IAC/C;AACA,QAAI,CAAC,IAAI;AACP,WAAK,OAAO;AAAA,IACd;AACA,QAAI,CAAC,MAAM,GAAG,WAAW,EAAG;AAC5B,QAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,KAAK,GAAG,SAAS,GAAG;AAC1G,WAAK,CAAC,GAAG,OAAO,GAAG,iBAAiB,OAAO,EAAE,CAAC;AAE9C,UAAI,GAAG,SAAS,GAAG;AACjB,aAAK,GAAG,KAAK,WAAS;AACpB,cAAI,eAAe,OAAO,SAAS,EAAE,CAAC,MAAM,OAAO,GAAI,QAAO;AAC9D,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,EAAE,KAAK,GAAG,WAAW,EAAG,MAAK,GAAG,CAAC;AACnD,WAAO,OAAO,OAAO,YAAY;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,UAAI,OAAO,SAAS,aAAa,OAAO,WAAW;AACjD,cAAM,UAAU,IAAI,IAAI,OAAO,kBAAkB,IAAI,MAAM,GAAG,CAAC;AAAA,MACjE;AACA,YAAM,UAAU,IAAI,OAAO,gBAAgB,OAAO,IAAI;AACtD,YAAM,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AACzF,UAAI,OAAO,SAAS,aAAa,OAAO,gBAAgB;AACtD,cAAM,UAAU,IAAI,GAAG,OAAO,aAAa,GAAG,OAAO,IAAI,UAAU;AACnE,6BAAqB;AACrB,YAAI,OAAO,qBAAqB,GAAG;AACjC,iBAAO,qBAAqB;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,OAAO,SAAS,iBAAiB,OAAO,qBAAqB;AAC/D,cAAM,UAAU,IAAI,OAAO,wBAAwB;AAAA,MACrD;AACA,UAAI,OAAO,WAAW;AACpB,cAAM,iBAAiB,SAAS,aAAa;AAAA,MAC/C;AACA,UAAI,CAAC,OAAO,SAAS;AACnB,cAAM,UAAU,IAAI,OAAO,SAAS;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,UAAU;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,qBAAqB,EAAG;AAC5B,QAAI,KAAK,OAAO,WAAW;AAC3B,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS;AAClB,cAAM,UAAU,OAAO,OAAO,WAAW;AACzC,cAAM,UAAU,OAAO,OAAO,gBAAgB,OAAO,IAAI;AACzD,cAAM,UAAU,OAAO,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AAC5F,YAAI,OAAO,WAAW;AACpB,gBAAM,UAAU,OAAO,IAAI,OAAO,kBAAkB,IAAI,MAAM,GAAG,CAAC;AAClE,gBAAM,oBAAoB,SAAS,aAAa;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW,QAAS,QAAO,WAAW,QAAQ,QAAQ,WAAS,MAAM,UAAU,OAAO,GAAG,OAAO,kBAAkB,MAAM,GAAG,CAAC,CAAC;AAAA,EAC1I;AACA,KAAG,mBAAmB,MAAM;AAC1B,QAAI,CAAC,OAAO,cAAc,CAAC,OAAO,WAAW,GAAI;AACjD,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,UAAU,OAAO,OAAO,iBAAiB,OAAO,aAAa;AACnE,YAAM,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AAAA,IAC3F,CAAC;AAAA,EACH,CAAC;AACD,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO;AAE9C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,qBAAqB,MAAM;AAC5B,QAAI,OAAO,OAAO,cAAc,aAAa;AAC3C,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,mBAAmB,MAAM;AAC1B,WAAO;AAAA,EACT,CAAC;AACD,KAAG,wBAAwB,MAAM;AAC/B,WAAO;AACP,WAAO;AAAA,EACT,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,OAAO,OAAO,WAAW,SAAS,CAAC;AAAA,IAC5G;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,WAAO;AAAA,EACT,CAAC;AACD,KAAG,SAAS,CAAC,IAAI,MAAM;AACrB,UAAM,WAAW,EAAE;AACnB,UAAM,KAAK,kBAAkB,OAAO,WAAW,EAAE;AACjD,QAAI,OAAO,OAAO,WAAW,MAAM,OAAO,OAAO,WAAW,eAAe,MAAM,GAAG,SAAS,KAAK,CAAC,SAAS,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW,GAAG;AACpK,UAAI,OAAO,eAAe,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW,UAAU,OAAO,WAAW,UAAU,aAAa,OAAO,WAAW,QAAS;AACnK,YAAM,WAAW,GAAG,CAAC,EAAE,UAAU,SAAS,OAAO,OAAO,WAAW,WAAW;AAC9E,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB;AAAA,MACvB,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AACA,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,OAAO,OAAO,WAAW,WAAW,CAAC;AAAA,IAClF;AAAA,EACF,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,OAAO,OAAO,WAAW,uBAAuB;AAC3E,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,OAAO,OAAO,OAAO,WAAW,uBAAuB,CAAC;AAAA,IAC9F;AACA,SAAK;AACL,WAAO;AACP,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,OAAO,OAAO,WAAW,uBAAuB;AACxE,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,WAAK,kBAAkB,EAAE;AACzB,SAAG,QAAQ,WAAS,MAAM,UAAU,IAAI,OAAO,OAAO,WAAW,uBAAuB,CAAC;AAAA,IAC3F;AACA,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACpcA,SAAS,UAAU,MAAM;AACvB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMC,YAAW,YAAY;AAC7B,MAAI,YAAY;AAChB,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,eAAa;AAAA,IACX,WAAW;AAAA,MACT,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO,YAAY;AAAA,IACjB,IAAI;AAAA,IACJ,QAAQ;AAAA,EACV;AACA,WAAS,eAAe;AACtB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,GAAI;AACzD,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,WAAW,OAAO,OAAO,OAAO,OAAO,eAAe,OAAO;AACnE,QAAI,UAAU;AACd,QAAI,UAAU,YAAY,YAAY;AACtC,QAAI,KAAK;AACP,eAAS,CAAC;AACV,UAAI,SAAS,GAAG;AACd,kBAAU,WAAW;AACrB,iBAAS;AAAA,MACX,WAAW,CAAC,SAAS,WAAW,WAAW;AACzC,kBAAU,YAAY;AAAA,MACxB;AAAA,IACF,WAAW,SAAS,GAAG;AACrB,gBAAU,WAAW;AACrB,eAAS;AAAA,IACX,WAAW,SAAS,WAAW,WAAW;AACxC,gBAAU,YAAY;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,MAAM,YAAY,eAAe,MAAM;AAC9C,aAAO,MAAM,QAAQ,GAAG,OAAO;AAAA,IACjC,OAAO;AACL,aAAO,MAAM,YAAY,oBAAoB,MAAM;AACnD,aAAO,MAAM,SAAS,GAAG,OAAO;AAAA,IAClC;AACA,QAAI,OAAO,MAAM;AACf,mBAAa,OAAO;AACpB,SAAG,MAAM,UAAU;AACnB,gBAAU,WAAW,MAAM;AACzB,WAAG,MAAM,UAAU;AACnB,WAAG,MAAM,qBAAqB;AAAA,MAChC,GAAG,GAAI;AAAA,IACT;AAAA,EACF;AACA,WAAS,cAAc,UAAU;AAC/B,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,GAAI;AACzD,WAAO,UAAU,OAAO,MAAM,qBAAqB,GAAG,QAAQ;AAAA,EAChE;AACA,WAAS,aAAa;AACpB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,GAAI;AACzD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,MAAM,QAAQ;AACrB,WAAO,MAAM,SAAS;AACtB,gBAAY,OAAO,aAAa,IAAI,GAAG,cAAc,GAAG;AACxD,cAAU,OAAO,QAAQ,OAAO,cAAc,OAAO,OAAO,sBAAsB,OAAO,OAAO,iBAAiB,OAAO,SAAS,CAAC,IAAI;AACtI,QAAI,OAAO,OAAO,UAAU,aAAa,QAAQ;AAC/C,iBAAW,YAAY;AAAA,IACzB,OAAO;AACL,iBAAW,SAAS,OAAO,OAAO,UAAU,UAAU,EAAE;AAAA,IAC1D;AACA,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,MAAM,QAAQ,GAAG,QAAQ;AAAA,IAClC,OAAO;AACL,aAAO,MAAM,SAAS,GAAG,QAAQ;AAAA,IACnC;AACA,QAAI,WAAW,GAAG;AAChB,SAAG,MAAM,UAAU;AAAA,IACrB,OAAO;AACL,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,UAAU,MAAM;AAChC,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,iBAAiB,OAAO,SAAS;AACjD,gBAAU,GAAG,UAAU,OAAO,WAAW,QAAQ,QAAQ,EAAE,OAAO,OAAO,UAAU,SAAS;AAAA,IAC9F;AAAA,EACF;AACA,WAAS,mBAAmB,GAAG;AAC7B,WAAO,OAAO,aAAa,IAAI,EAAE,UAAU,EAAE;AAAA,EAC/C;AACA,WAAS,gBAAgB,GAAG;AAC1B,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,qBAAiB,mBAAmB,CAAC,IAAI,cAAc,EAAE,EAAE,OAAO,aAAa,IAAI,SAAS,KAAK,KAAK,iBAAiB,OAAO,eAAe,WAAW,OAAO,YAAY;AAC3K,oBAAgB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,GAAG,CAAC;AACtD,QAAI,KAAK;AACP,sBAAgB,IAAI;AAAA,IACtB;AACA,UAAM,WAAW,OAAO,aAAa,KAAK,OAAO,aAAa,IAAI,OAAO,aAAa,KAAK;AAC3F,WAAO,eAAe,QAAQ;AAC9B,WAAO,aAAa,QAAQ;AAC5B,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AACA,WAAS,YAAY,GAAG;AACtB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,gBAAY;AACZ,mBAAe,EAAE,WAAW,SAAS,mBAAmB,CAAC,IAAI,EAAE,OAAO,sBAAsB,EAAE,OAAO,aAAa,IAAI,SAAS,KAAK,IAAI;AACxI,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,cAAU,MAAM,qBAAqB;AACrC,WAAO,MAAM,qBAAqB;AAClC,oBAAgB,CAAC;AACjB,iBAAa,WAAW;AACxB,OAAG,MAAM,qBAAqB;AAC9B,QAAI,OAAO,MAAM;AACf,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,kBAAkB,IAAI;AAAA,IAC/C;AACA,SAAK,sBAAsB,CAAC;AAAA,EAC9B;AACA,WAAS,WAAW,GAAG;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAW;AAChB,QAAI,EAAE,kBAAkB,EAAE,WAAY,GAAE,eAAe;AAAA,QAAO,GAAE,cAAc;AAC9E,oBAAgB,CAAC;AACjB,cAAU,MAAM,qBAAqB;AACrC,OAAG,MAAM,qBAAqB;AAC9B,WAAO,MAAM,qBAAqB;AAClC,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AACA,WAAS,UAAU,GAAG;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAW;AAChB,gBAAY;AACZ,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,kBAAkB,IAAI;AAC7C,gBAAU,MAAM,qBAAqB;AAAA,IACvC;AACA,QAAI,OAAO,MAAM;AACf,mBAAa,WAAW;AACxB,oBAAc,SAAS,MAAM;AAC3B,WAAG,MAAM,UAAU;AACnB,WAAG,MAAM,qBAAqB;AAAA,MAChC,GAAG,GAAI;AAAA,IACT;AACA,SAAK,oBAAoB,CAAC;AAC1B,QAAI,OAAO,eAAe;AACxB,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AACA,WAAS,OAAO,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,KAAK,UAAU;AACrB,QAAI,CAAC,GAAI;AACT,UAAM,SAAS;AACf,UAAM,iBAAiB,OAAO,mBAAmB;AAAA,MAC/C,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,UAAM,kBAAkB,OAAO,mBAAmB;AAAA,MAChD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,QAAI,CAAC,OAAQ;AACb,UAAM,cAAc,WAAW,OAAO,qBAAqB;AAC3D,WAAO,WAAW,EAAE,eAAe,aAAa,cAAc;AAC9D,IAAAA,UAAS,WAAW,EAAE,eAAe,YAAY,cAAc;AAC/D,IAAAA,UAAS,WAAW,EAAE,aAAa,WAAW,eAAe;AAAA,EAC/D;AACA,WAAS,kBAAkB;AACzB,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,GAAI;AACzD,WAAO,IAAI;AAAA,EACb;AACA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,OAAO,OAAO,UAAU,MAAM,CAAC,OAAO,UAAU,GAAI;AACzD,WAAO,KAAK;AAAA,EACd;AACA,WAAS,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA,IAAI;AAAA,IACN,IAAI;AACJ,WAAO,OAAO,YAAY,0BAA0B,QAAQ,OAAO,eAAe,WAAW,OAAO,OAAO,WAAW;AAAA,MACpH,IAAI;AAAA,IACN,CAAC;AACD,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,OAAO,GAAI;AAChB,QAAI;AACJ,QAAI,OAAO,OAAO,OAAO,YAAY,OAAO,WAAW;AACrD,WAAK,OAAO,GAAG,cAAc,OAAO,EAAE;AAAA,IACxC;AACA,QAAI,CAAC,MAAM,OAAO,OAAO,OAAO,UAAU;AACxC,WAAKA,UAAS,iBAAiB,OAAO,EAAE;AACxC,UAAI,CAAC,GAAG,OAAQ;AAAA,IAClB,WAAW,CAAC,IAAI;AACd,WAAK,OAAO;AAAA,IACd;AACA,QAAI,OAAO,OAAO,qBAAqB,OAAO,OAAO,OAAO,YAAY,GAAG,SAAS,KAAK,SAAS,iBAAiB,OAAO,EAAE,EAAE,WAAW,GAAG;AAC1I,WAAK,SAAS,cAAc,OAAO,EAAE;AAAA,IACvC;AACA,QAAI,GAAG,SAAS,EAAG,MAAK,GAAG,CAAC;AAC5B,OAAG,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AACtF,QAAI;AACJ,QAAI,IAAI;AACN,eAAS,GAAG,cAAc,kBAAkB,OAAO,OAAO,UAAU,SAAS,CAAC;AAC9E,UAAI,CAAC,QAAQ;AACX,iBAAS,cAAc,OAAO,OAAO,OAAO,UAAU,SAAS;AAC/D,WAAG,OAAO,MAAM;AAAA,MAClB;AAAA,IACF;AACA,WAAO,OAAO,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,OAAO,WAAW;AACpB,sBAAgB;AAAA,IAClB;AACA,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,GAAG,gBAAgB,OAAO,OAAO,UAAU,SAAS,CAAC;AAAA,IACvG;AAAA,EACF;AACA,WAAS,UAAU;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,KAAK,OAAO,UAAU;AAC5B,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,GAAG,gBAAgB,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa,CAAC;AAAA,IAC/G;AACA,qBAAiB;AAAA,EACnB;AACA,KAAG,mBAAmB,MAAM;AAC1B,QAAI,CAAC,OAAO,aAAa,CAAC,OAAO,UAAU,GAAI;AAC/C,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI;AAAA,MACF;AAAA,IACF,IAAI,OAAO;AACX,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,UAAU,OAAO,OAAO,iBAAiB,OAAO,aAAa;AACnE,YAAM,UAAU,IAAI,OAAO,aAAa,IAAI,OAAO,kBAAkB,OAAO,aAAa;AAAA,IAC3F,CAAC;AAAA,EACH,CAAC;AACD,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,UAAU,YAAY,OAAO;AAE7C,cAAQ;AAAA,IACV,OAAO;AACL,WAAK;AACL,iBAAW;AACX,mBAAa;AAAA,IACf;AAAA,EACF,CAAC;AACD,KAAG,4DAA4D,MAAM;AACnE,eAAW;AAAA,EACb,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,kBAAc,QAAQ;AAAA,EACxB,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,QAAI,IAAI;AACN,SAAG,UAAU,OAAO,UAAU,WAAW,KAAK,EAAE,GAAG,gBAAgB,OAAO,OAAO,UAAU,SAAS,CAAC;AAAA,IACvG;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,QAAM,SAAS,MAAM;AACnB,WAAO,GAAG,UAAU,OAAO,GAAG,gBAAgB,OAAO,OAAO,UAAU,sBAAsB,CAAC;AAC7F,QAAI,OAAO,UAAU,IAAI;AACvB,aAAO,UAAU,GAAG,UAAU,OAAO,GAAG,gBAAgB,OAAO,OAAO,UAAU,sBAAsB,CAAC;AAAA,IACzG;AACA,SAAK;AACL,eAAW;AACX,iBAAa;AAAA,EACf;AACA,QAAM,UAAU,MAAM;AACpB,WAAO,GAAG,UAAU,IAAI,GAAG,gBAAgB,OAAO,OAAO,UAAU,sBAAsB,CAAC;AAC1F,QAAI,OAAO,UAAU,IAAI;AACvB,aAAO,UAAU,GAAG,UAAU,IAAI,GAAG,gBAAgB,OAAO,OAAO,UAAU,sBAAsB,CAAC;AAAA,IACtG;AACA,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,OAAO,WAAW;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACzWA,SAAS,SAAS,MAAM;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB;AACzB,QAAM,eAAe,CAAC,IAAI,aAAa;AACrC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,MAAM,KAAK;AAC7B,UAAM,IAAI,GAAG,aAAa,sBAAsB,KAAK;AACrD,QAAI,IAAI,GAAG,aAAa,wBAAwB;AAChD,QAAI,IAAI,GAAG,aAAa,wBAAwB;AAChD,UAAM,QAAQ,GAAG,aAAa,4BAA4B;AAC1D,UAAM,UAAU,GAAG,aAAa,8BAA8B;AAC9D,UAAM,SAAS,GAAG,aAAa,6BAA6B;AAC5D,QAAI,KAAK,GAAG;AACV,UAAI,KAAK;AACT,UAAI,KAAK;AAAA,IACX,WAAW,OAAO,aAAa,GAAG;AAChC,UAAI;AACJ,UAAI;AAAA,IACN,OAAO;AACL,UAAI;AACJ,UAAI;AAAA,IACN;AACA,QAAI,EAAE,QAAQ,GAAG,KAAK,GAAG;AACvB,UAAI,GAAG,SAAS,GAAG,EAAE,IAAI,WAAW,SAAS;AAAA,IAC/C,OAAO;AACL,UAAI,GAAG,IAAI,WAAW,SAAS;AAAA,IACjC;AACA,QAAI,EAAE,QAAQ,GAAG,KAAK,GAAG;AACvB,UAAI,GAAG,SAAS,GAAG,EAAE,IAAI,QAAQ;AAAA,IACnC,OAAO;AACL,UAAI,GAAG,IAAI,QAAQ;AAAA,IACrB;AACA,QAAI,OAAO,YAAY,eAAe,YAAY,MAAM;AACtD,YAAM,iBAAiB,WAAW,UAAU,MAAM,IAAI,KAAK,IAAI,QAAQ;AACvE,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,QAAI,YAAY,eAAe,CAAC,KAAK,CAAC;AACtC,QAAI,OAAO,UAAU,eAAe,UAAU,MAAM;AAClD,YAAM,eAAe,SAAS,QAAQ,MAAM,IAAI,KAAK,IAAI,QAAQ;AACjE,mBAAa,UAAU,YAAY;AAAA,IACrC;AACA,QAAI,UAAU,OAAO,WAAW,eAAe,WAAW,MAAM;AAC9D,YAAM,gBAAgB,SAAS,WAAW;AAC1C,mBAAa,WAAW,aAAa;AAAA,IACvC;AACA,OAAG,MAAM,YAAY;AAAA,EACvB;AACA,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,gBAAgB,IAAI,gBAAgB;AACrD,QAAI,OAAO,WAAW;AACpB,eAAS,KAAK,GAAG,gBAAgB,OAAO,QAAQ,gBAAgB,CAAC;AAAA,IACnE;AACA,aAAS,QAAQ,WAAS;AACxB,mBAAa,OAAO,QAAQ;AAAA,IAC9B,CAAC;AACD,WAAO,QAAQ,CAAC,SAAS,eAAe;AACtC,UAAI,gBAAgB,QAAQ;AAC5B,UAAI,OAAO,OAAO,iBAAiB,KAAK,OAAO,OAAO,kBAAkB,QAAQ;AAC9E,yBAAiB,KAAK,KAAK,aAAa,CAAC,IAAI,YAAY,SAAS,SAAS;AAAA,MAC7E;AACA,sBAAgB,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE,GAAG,CAAC;AACvD,cAAQ,iBAAiB,GAAG,gBAAgB,iCAAiC,EAAE,QAAQ,WAAS;AAC9F,qBAAa,OAAO,aAAa;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,SAAU,UAAU;AACxC,QAAI,aAAa,QAAQ;AACvB,iBAAW,OAAO,OAAO;AAAA,IAC3B;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,CAAC,GAAG,GAAG,iBAAiB,gBAAgB,CAAC;AAC1D,QAAI,OAAO,WAAW;AACpB,eAAS,KAAK,GAAG,OAAO,iBAAiB,gBAAgB,CAAC;AAAA,IAC5D;AACA,aAAS,QAAQ,gBAAc;AAC7B,UAAI,mBAAmB,SAAS,WAAW,aAAa,+BAA+B,GAAG,EAAE,KAAK;AACjG,UAAI,aAAa,EAAG,oBAAmB;AACvC,iBAAW,MAAM,qBAAqB,GAAG,gBAAgB;AAAA,IAC3D,CAAC;AAAA,EACH;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,CAAC,OAAO,OAAO,SAAS,QAAS;AACrC,WAAO,OAAO,sBAAsB;AACpC,WAAO,eAAe,sBAAsB;AAAA,EAC9C,CAAC;AACD,KAAG,QAAQ,MAAM;AACf,QAAI,CAAC,OAAO,OAAO,SAAS,QAAS;AACrC,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,gBAAgB,MAAM;AACvB,QAAI,CAAC,OAAO,OAAO,SAAS,QAAS;AACrC,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,SAAS,aAAa;AACzC,QAAI,CAAC,OAAO,OAAO,SAAS,QAAS;AACrC,kBAAc,QAAQ;AAAA,EACxB,CAAC;AACH;;;ACtHA,SAAS,KAAK,MAAM;AAClB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMC,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO,OAAO;AAAA,IACZ,SAAS;AAAA,EACX;AACA,MAAI,eAAe;AACnB,MAAI,YAAY;AAChB,MAAI,qBAAqB;AACzB,MAAI,gBAAgB;AAAA,IAClB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,sBAAsB;AAC5B,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU;AAAA,EACZ;AACA,QAAM,QAAQ;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc,CAAC;AAAA,IACf,gBAAgB,CAAC;AAAA,EACnB;AACA,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,IACH,eAAe;AAAA,IACf,eAAe;AAAA,IACf,UAAU;AAAA,EACZ;AACA,MAAI,QAAQ;AACZ,SAAO,eAAe,OAAO,MAAM,SAAS;AAAA,IAC1C,MAAM;AACJ,aAAO;AAAA,IACT;AAAA,IACA,IAAI,OAAO;AACT,UAAI,UAAU,OAAO;AACnB,cAAM,UAAU,QAAQ;AACxB,cAAM,UAAU,QAAQ;AACxB,aAAK,cAAc,OAAO,SAAS,OAAO;AAAA,MAC5C;AACA,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,WAAS,4BAA4B;AACnC,QAAI,QAAQ,SAAS,EAAG,QAAO;AAC/B,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,WAAW,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,CAAC;AAC1D,WAAO;AAAA,EACT;AACA,WAAS,cAAc;AACrB,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,WAAW,QAAQ,YAAY,aAAa,kBAAkB,KAAK,OAAO;AAChF,QAAI,OAAO,uBAAuB,QAAQ,WAAW,QAAQ,QAAQ,cAAc;AACjF,YAAM,gBAAgB,QAAQ,QAAQ,eAAe,QAAQ,QAAQ;AACrE,aAAO,KAAK,IAAI,eAAe,QAAQ;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,WAAS,iBAAiB;AACxB,QAAI,QAAQ,SAAS,EAAG,QAAO;AAAA,MAC7B,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,MAAM,QAAQ,QAAQ,sBAAsB;AAClD,WAAO,EAAE,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ,QAAQ,CAAC,EAAE,SAAS,IAAI,IAAI,IAAIA,QAAO,WAAW,eAAe,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ,QAAQ,CAAC,EAAE,SAAS,IAAI,IAAI,IAAIA,QAAO,WAAW,YAAY;AAAA,EACxN;AACA,WAAS,mBAAmB;AAC1B,WAAO,OAAO,YAAY,iBAAiB,IAAI,OAAO,OAAO,UAAU;AAAA,EACzE;AACA,WAAS,iBAAiB,GAAG;AAC3B,UAAM,gBAAgB,iBAAiB;AACvC,QAAI,EAAE,OAAO,QAAQ,aAAa,EAAG,QAAO;AAC5C,QAAI,OAAO,OAAO,OAAO,aAAW,QAAQ,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,EAAG,QAAO;AACnF,WAAO;AAAA,EACT;AACA,WAAS,yBAAyB,GAAG;AACnC,UAAM,WAAW,IAAI,OAAO,OAAO,KAAK,cAAc;AACtD,QAAI,EAAE,OAAO,QAAQ,QAAQ,EAAG,QAAO;AACvC,QAAI,CAAC,GAAG,OAAO,OAAO,iBAAiB,QAAQ,CAAC,EAAE,OAAO,iBAAe,YAAY,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,EAAG,QAAO;AAC3H,WAAO;AAAA,EACT;AAGA,WAAS,eAAe,GAAG;AACzB,QAAI,EAAE,gBAAgB,SAAS;AAC7B,cAAQ,OAAO,GAAG,QAAQ,MAAM;AAAA,IAClC;AACA,QAAI,CAAC,iBAAiB,CAAC,EAAG;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,yBAAqB;AACrB,uBAAmB;AACnB,YAAQ,KAAK,CAAC;AACd,QAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,IACF;AACA,yBAAqB;AACrB,YAAQ,aAAa,0BAA0B;AAC/C,QAAI,CAAC,QAAQ,SAAS;AACpB,cAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,UAAU,gBAAgB;AAC/E,UAAI,CAAC,QAAQ,QAAS,SAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AACxE,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,cAAc,EAAE;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,cAAc,EAAE,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AACA,UAAI,CAAC,QAAQ,aAAa;AACxB,gBAAQ,UAAU;AAClB;AAAA,MACF;AACA,cAAQ,WAAW,YAAY;AAAA,IACjC;AACA,QAAI,QAAQ,SAAS;AACnB,YAAM,CAAC,SAAS,OAAO,IAAI,eAAe;AAC1C,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,QAAQ,MAAM,qBAAqB;AAAA,IAC7C;AACA,gBAAY;AAAA,EACd;AACA,WAAS,gBAAgB,GAAG;AAC1B,QAAI,CAAC,iBAAiB,CAAC,EAAG;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,OAAO,OAAO;AACpB,UAAM,eAAe,QAAQ,UAAU,cAAY,SAAS,cAAc,EAAE,SAAS;AACrF,QAAI,gBAAgB,EAAG,SAAQ,YAAY,IAAI;AAC/C,QAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,IACF;AACA,uBAAmB;AACnB,YAAQ,YAAY,0BAA0B;AAC9C,QAAI,CAAC,QAAQ,SAAS;AACpB;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ,YAAY,QAAQ,aAAa;AACtD,QAAI,KAAK,QAAQ,QAAQ,UAAU;AACjC,WAAK,QAAQ,QAAQ,WAAW,KAAK,KAAK,QAAQ,QAAQ,WAAW,MAAM;AAAA,IAC7E;AACA,QAAI,KAAK,QAAQ,OAAO,UAAU;AAChC,WAAK,QAAQ,OAAO,WAAW,KAAK,OAAO,WAAW,KAAK,QAAQ,MAAM;AAAA,IAC3E;AACA,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK,KAAK;AAAA,EAC1E;AACA,WAAS,aAAa,GAAG;AACvB,QAAI,CAAC,iBAAiB,CAAC,EAAG;AAC1B,QAAI,EAAE,gBAAgB,WAAW,EAAE,SAAS,aAAc;AAC1D,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,OAAO,OAAO;AACpB,UAAM,eAAe,QAAQ,UAAU,cAAY,SAAS,cAAc,EAAE,SAAS;AACrF,QAAI,gBAAgB,EAAG,SAAQ,OAAO,cAAc,CAAC;AACrD,QAAI,CAAC,sBAAsB,CAAC,kBAAkB;AAC5C;AAAA,IACF;AACA,yBAAqB;AACrB,uBAAmB;AACnB,QAAI,CAAC,QAAQ,QAAS;AACtB,SAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,QAAQ,QAAQ,GAAG,OAAO,QAAQ;AAC7E,YAAQ,QAAQ,MAAM,qBAAqB,GAAG,OAAO,OAAO,KAAK;AACjE,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK,KAAK;AACxE,mBAAe,KAAK;AACpB,gBAAY;AACZ,QAAI,KAAK,QAAQ,KAAK,QAAQ,SAAS;AACrC,cAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,gBAAgB,EAAE;AAAA,IAC5D,WAAW,KAAK,SAAS,KAAK,QAAQ,SAAS;AAC7C,cAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,gBAAgB,EAAE;AAAA,IAC/D;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,MAAI;AACJ,WAAS,iBAAiB;AACxB,WAAO,gBAAgB,kCAAkC;AAAA,EAC3D;AACA,WAAS,mBAAmB;AAC1B,iBAAa,qBAAqB;AAClC,WAAO,gBAAgB,kCAAkC;AACzD,4BAAwB,WAAW,MAAM;AACvC,UAAI,OAAO,UAAW;AACtB,qBAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,WAAS,aAAa,GAAG;AACvB,UAAM,SAAS,OAAO;AACtB,QAAI,CAAC,QAAQ,QAAS;AACtB,QAAI,MAAM,UAAW;AACrB,QAAI,OAAO,WAAW,EAAE,WAAY,GAAE,eAAe;AACrD,UAAM,YAAY;AAClB,UAAMC,SAAQ,QAAQ,SAAS,IAAI,QAAQ,CAAC,IAAI;AAChD,UAAM,aAAa,IAAIA,OAAM;AAC7B,UAAM,aAAa,IAAIA,OAAM;AAAA,EAC/B;AACA,WAAS,YAAY,GAAG;AACtB,UAAM,eAAe,EAAE,gBAAgB;AACvC,UAAM,aAAa,gBAAgB,OAAO,OAAO,KAAK;AACtD,QAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG;AACxD;AAAA,IACF;AACA,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,QAAQ,SAAS;AACpB;AAAA,IACF;AACA,QAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,SAAS;AACxC,UAAI,WAAY,aAAY,CAAC;AAC7B;AAAA,IACF;AACA,QAAI,YAAY;AACd,kBAAY,CAAC;AACb;AAAA,IACF;AACA,QAAI,CAAC,MAAM,SAAS;AAClB,YAAM,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,QAAQ;AAC7D,YAAM,SAAS,QAAQ,QAAQ,gBAAgB,QAAQ,QAAQ;AAC/D,YAAM,SAAS,aAAa,QAAQ,aAAa,GAAG,KAAK;AACzD,YAAM,SAAS,aAAa,QAAQ,aAAa,GAAG,KAAK;AACzD,cAAQ,aAAa,QAAQ,QAAQ;AACrC,cAAQ,cAAc,QAAQ,QAAQ;AACtC,cAAQ,YAAY,MAAM,qBAAqB;AAAA,IACjD;AAEA,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,eAAe,MAAM,SAAS,KAAK;AACzC,UAAM,OAAO,KAAK,IAAI,QAAQ,aAAa,IAAI,cAAc,GAAG,CAAC;AACjE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,OAAO,KAAK,IAAI,QAAQ,cAAc,IAAI,eAAe,GAAG,CAAC;AACnE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,eAAe,IAAI,QAAQ,SAAS,IAAI,QAAQ,CAAC,EAAE,QAAQ,EAAE;AACnE,UAAM,eAAe,IAAI,QAAQ,SAAS,IAAI,QAAQ,CAAC,EAAE,QAAQ,EAAE;AACnE,UAAM,cAAc,KAAK,IAAI,KAAK,IAAI,MAAM,eAAe,IAAI,MAAM,aAAa,CAAC,GAAG,KAAK,IAAI,MAAM,eAAe,IAAI,MAAM,aAAa,CAAC,CAAC;AAC7I,QAAI,cAAc,GAAG;AACnB,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,CAAC,MAAM,WAAW,CAAC,WAAW;AAChC,UAAI,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI;AAC3O,cAAM,YAAY;AAClB,uBAAe;AACf;AAAA,MACF;AACA,UAAI,CAAC,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI;AAC5O,cAAM,YAAY;AAClB,uBAAe;AACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,YAAY;AAChB,QAAE,eAAe;AAAA,IACnB;AACA,MAAE,gBAAgB;AAClB,qBAAiB;AACjB,UAAM,UAAU;AAChB,UAAM,cAAc,KAAK,QAAQ,iBAAiB,QAAQ,WAAW,OAAO,OAAO,KAAK;AACxF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI,MAAM,SAAS,cAAc,MAAM,QAAQ,UAAU;AACtH,UAAM,WAAW,MAAM,eAAe,IAAI,MAAM,aAAa,IAAI,MAAM,SAAS,cAAc,MAAM,SAAS,UAAU;AACvH,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,WAAW,MAAM,OAAO,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,OAAO,MAAM,WAAW,MAAM;AAAA,IACzE;AACA,QAAI,MAAM,WAAW,MAAM,MAAM;AAC/B,YAAM,WAAW,MAAM,OAAO,KAAK,MAAM,WAAW,MAAM,OAAO,MAAM;AAAA,IACzE;AAGA,QAAI,CAAC,SAAS,cAAe,UAAS,gBAAgB,MAAM,eAAe;AAC3E,QAAI,CAAC,SAAS,cAAe,UAAS,gBAAgB,MAAM,eAAe;AAC3E,QAAI,CAAC,SAAS,SAAU,UAAS,WAAW,KAAK,IAAI;AACrD,aAAS,KAAK,MAAM,eAAe,IAAI,SAAS,kBAAkB,KAAK,IAAI,IAAI,SAAS,YAAY;AACpG,aAAS,KAAK,MAAM,eAAe,IAAI,SAAS,kBAAkB,KAAK,IAAI,IAAI,SAAS,YAAY;AACpG,QAAI,KAAK,IAAI,MAAM,eAAe,IAAI,SAAS,aAAa,IAAI,EAAG,UAAS,IAAI;AAChF,QAAI,KAAK,IAAI,MAAM,eAAe,IAAI,SAAS,aAAa,IAAI,EAAG,UAAS,IAAI;AAChF,aAAS,gBAAgB,MAAM,eAAe;AAC9C,aAAS,gBAAgB,MAAM,eAAe;AAC9C,aAAS,WAAW,KAAK,IAAI;AAC7B,YAAQ,YAAY,MAAM,YAAY,eAAe,MAAM,QAAQ,OAAO,MAAM,QAAQ;AAAA,EAC1F;AACA,WAAS,aAAa;AACpB,UAAM,OAAO,OAAO;AACpB,YAAQ,SAAS;AACjB,QAAI,CAAC,QAAQ,QAAS;AACtB,QAAI,CAAC,MAAM,aAAa,CAAC,MAAM,SAAS;AACtC,YAAM,YAAY;AAClB,YAAM,UAAU;AAChB;AAAA,IACF;AACA,UAAM,YAAY;AAClB,UAAM,UAAU;AAChB,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AACxB,UAAM,oBAAoB,SAAS,IAAI;AACvC,UAAM,eAAe,MAAM,WAAW;AACtC,UAAM,oBAAoB,SAAS,IAAI;AACvC,UAAM,eAAe,MAAM,WAAW;AAGtC,QAAI,SAAS,MAAM,EAAG,qBAAoB,KAAK,KAAK,eAAe,MAAM,YAAY,SAAS,CAAC;AAC/F,QAAI,SAAS,MAAM,EAAG,qBAAoB,KAAK,KAAK,eAAe,MAAM,YAAY,SAAS,CAAC;AAC/F,UAAM,mBAAmB,KAAK,IAAI,mBAAmB,iBAAiB;AACtE,UAAM,WAAW;AACjB,UAAM,WAAW;AAEjB,UAAM,cAAc,MAAM,QAAQ,KAAK;AACvC,UAAM,eAAe,MAAM,SAAS,KAAK;AACzC,UAAM,OAAO,KAAK,IAAI,QAAQ,aAAa,IAAI,cAAc,GAAG,CAAC;AACjE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,OAAO,KAAK,IAAI,QAAQ,cAAc,IAAI,eAAe,GAAG,CAAC;AACnE,UAAM,OAAO,CAAC,MAAM;AACpB,UAAM,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,MAAM,IAAI;AAC1E,UAAM,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,MAAM,IAAI,GAAG,MAAM,IAAI;AAC1E,YAAQ,YAAY,MAAM,qBAAqB,GAAG,gBAAgB;AAClE,YAAQ,YAAY,MAAM,YAAY,eAAe,MAAM,QAAQ,OAAO,MAAM,QAAQ;AAAA,EAC1F;AACA,WAAS,kBAAkB;AACzB,UAAM,OAAO,OAAO;AACpB,QAAI,QAAQ,WAAW,OAAO,gBAAgB,OAAO,OAAO,QAAQ,QAAQ,OAAO,GAAG;AACpF,UAAI,QAAQ,SAAS;AACnB,gBAAQ,QAAQ,MAAM,YAAY;AAAA,MACpC;AACA,UAAI,QAAQ,aAAa;AACvB,gBAAQ,YAAY,MAAM,YAAY;AAAA,MACxC;AACA,cAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,OAAO,KAAK,gBAAgB,EAAE;AACzE,WAAK,QAAQ;AACb,qBAAe;AACf,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAClB,cAAQ,cAAc;AACtB,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,WAAS,YAAY,GAAG;AAEtB,QAAI,gBAAgB,KAAK,CAAC,QAAQ,YAAa;AAC/C,QAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,yBAAyB,CAAC,EAAG;AAC1D,UAAM,mBAAmBD,QAAO,iBAAiB,QAAQ,WAAW,EAAE;AACtE,UAAM,SAAS,IAAIA,QAAO,UAAU,gBAAgB;AACpD,QAAI,CAAC,oBAAoB;AACvB,2BAAqB;AACrB,oBAAc,IAAI,EAAE;AACpB,oBAAc,IAAI,EAAE;AACpB,YAAM,SAAS,OAAO;AACtB,YAAM,SAAS,OAAO;AACtB,YAAM,QAAQ,QAAQ,QAAQ,eAAe,QAAQ,QAAQ;AAC7D,YAAM,SAAS,QAAQ,QAAQ,gBAAgB,QAAQ,QAAQ;AAC/D,cAAQ,aAAa,QAAQ,QAAQ;AACrC,cAAQ,cAAc,QAAQ,QAAQ;AACtC;AAAA,IACF;AACA,UAAM,UAAU,EAAE,UAAU,cAAc,KAAK;AAC/C,UAAM,UAAU,EAAE,UAAU,cAAc,KAAK;AAC/C,UAAM,cAAc,MAAM,QAAQ;AAClC,UAAM,eAAe,MAAM,SAAS;AACpC,UAAM,aAAa,QAAQ;AAC3B,UAAM,cAAc,QAAQ;AAC5B,UAAM,OAAO,KAAK,IAAI,aAAa,IAAI,cAAc,GAAG,CAAC;AACzD,UAAM,OAAO,CAAC;AACd,UAAM,OAAO,KAAK,IAAI,cAAc,IAAI,eAAe,GAAG,CAAC;AAC3D,UAAM,OAAO,CAAC;AACd,UAAM,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,IAAI,GAAG,IAAI;AACjE,UAAM,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,IAAI,GAAG,IAAI;AACjE,YAAQ,YAAY,MAAM,qBAAqB;AAC/C,YAAQ,YAAY,MAAM,YAAY,eAAe,IAAI,OAAO,IAAI;AACpE,kBAAc,IAAI,EAAE;AACpB,kBAAc,IAAI,EAAE;AACpB,UAAM,SAAS;AACf,UAAM,SAAS;AACf,UAAM,WAAW;AACjB,UAAM,WAAW;AAAA,EACnB;AACA,WAAS,OAAO,GAAG;AACjB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,QAAQ,SAAS;AACpB,UAAI,KAAK,EAAE,QAAQ;AACjB,gBAAQ,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,UAAU,gBAAgB;AAAA,MACjF;AACA,UAAI,CAAC,QAAQ,SAAS;AACpB,YAAI,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,kBAAQ,UAAU,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,gBAAgB,EAAE,EAAE,CAAC;AAAA,QAC5F,OAAO;AACL,kBAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AAAA,QACpD;AAAA,MACF;AACA,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,cAAc,EAAE;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,cAAc,EAAE,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,YAAa;AAC9C,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,WAAW;AAClC,aAAO,UAAU,MAAM,cAAc;AAAA,IACvC;AACA,YAAQ,QAAQ,UAAU,IAAI,GAAG,OAAO,gBAAgB,EAAE;AAC1D,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,MAAM,aAAa,MAAM,eAAe,GAAG;AACpD,eAAS,EAAE;AACX,eAAS,EAAE;AAAA,IACb,OAAO;AACL,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,aAAa;AAAA,IAC9B;AACA,UAAM,YAAY;AAClB,UAAM,iBAAiB,OAAO,MAAM,WAAW,IAAI;AACnD,QAAI,iBAAiB,KAAK,gBAAgB;AACxC,eAAS;AACT,eAAS;AACT,YAAM,aAAa,IAAI;AACvB,YAAM,aAAa,IAAI;AAAA,IACzB;AACA,UAAM,WAAW,YAAY;AAC7B,SAAK,QAAQ,kBAAkB;AAC/B,mBAAe,kBAAkB;AACjC,QAAI,KAAK,EAAE,iBAAiB,KAAK,iBAAiB;AAChD,mBAAa,QAAQ,QAAQ;AAC7B,oBAAc,QAAQ,QAAQ;AAC9B,gBAAU,cAAc,QAAQ,OAAO,EAAE,OAAOA,QAAO;AACvD,gBAAU,cAAc,QAAQ,OAAO,EAAE,MAAMA,QAAO;AACtD,cAAQ,UAAU,aAAa,IAAI;AACnC,cAAQ,UAAU,cAAc,IAAI;AACpC,mBAAa,QAAQ,QAAQ,eAAe,QAAQ,QAAQ;AAC5D,oBAAc,QAAQ,QAAQ,gBAAgB,QAAQ,QAAQ;AAC9D,oBAAc,aAAa,KAAK;AAChC,qBAAe,cAAc,KAAK;AAClC,sBAAgB,KAAK,IAAI,aAAa,IAAI,cAAc,GAAG,CAAC;AAC5D,sBAAgB,KAAK,IAAI,cAAc,IAAI,eAAe,GAAG,CAAC;AAC9D,sBAAgB,CAAC;AACjB,sBAAgB,CAAC;AACjB,UAAI,YAAY,KAAK,kBAAkB,OAAO,MAAM,aAAa,YAAY,OAAO,MAAM,aAAa,UAAU;AAC/G,qBAAa,MAAM,WAAW,KAAK,QAAQ;AAC3C,qBAAa,MAAM,WAAW,KAAK,QAAQ;AAAA,MAC7C,OAAO;AACL,qBAAa,QAAQ,KAAK;AAC1B,qBAAa,QAAQ,KAAK;AAAA,MAC5B;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,eAAe;AAC9B,qBAAa;AAAA,MACf;AAAA,IACF,OAAO;AACL,mBAAa;AACb,mBAAa;AAAA,IACf;AACA,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,cAAQ,UAAU;AAClB,cAAQ,UAAU;AAAA,IACpB;AACA,UAAM,WAAW;AACjB,UAAM,WAAW;AACjB,YAAQ,YAAY,MAAM,qBAAqB;AAC/C,YAAQ,YAAY,MAAM,YAAY,eAAe,UAAU,OAAO,UAAU;AAChF,YAAQ,QAAQ,MAAM,qBAAqB;AAC3C,YAAQ,QAAQ,MAAM,YAAY,4BAA4B,KAAK,KAAK;AAAA,EAC1E;AACA,WAAS,UAAU;AACjB,UAAM,OAAO,OAAO;AACpB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,QAAQ,SAAS;AACpB,UAAI,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,SAAS;AAC5E,gBAAQ,UAAU,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,gBAAgB,EAAE,EAAE,CAAC;AAAA,MAC5F,OAAO;AACL,gBAAQ,UAAU,OAAO,OAAO,OAAO,WAAW;AAAA,MACpD;AACA,UAAI,UAAU,QAAQ,QAAQ,cAAc,IAAI,OAAO,cAAc,EAAE;AACvE,UAAI,SAAS;AACX,kBAAU,QAAQ,iBAAiB,gDAAgD,EAAE,CAAC;AAAA,MACxF;AACA,cAAQ,UAAU;AAClB,UAAI,SAAS;AACX,gBAAQ,cAAc,eAAe,QAAQ,SAAS,IAAI,OAAO,cAAc,EAAE,EAAE,CAAC;AAAA,MACtF,OAAO;AACL,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,YAAa;AAC9C,QAAI,OAAO,OAAO,SAAS;AACzB,aAAO,UAAU,MAAM,WAAW;AAClC,aAAO,UAAU,MAAM,cAAc;AAAA,IACvC;AACA,SAAK,QAAQ;AACb,mBAAe;AACf,UAAM,WAAW;AACjB,UAAM,WAAW;AACjB,UAAM,aAAa,IAAI;AACvB,UAAM,aAAa,IAAI;AACvB,YAAQ,YAAY,MAAM,qBAAqB;AAC/C,YAAQ,YAAY,MAAM,YAAY;AACtC,YAAQ,QAAQ,MAAM,qBAAqB;AAC3C,YAAQ,QAAQ,MAAM,YAAY;AAClC,YAAQ,QAAQ,UAAU,OAAO,GAAG,OAAO,gBAAgB,EAAE;AAC7D,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAClB,QAAI,OAAO,OAAO,KAAK,gBAAgB;AACrC,sBAAgB;AAAA,QACd,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,oBAAoB;AACtB,6BAAqB;AACrB,cAAM,SAAS;AACf,cAAM,SAAS;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAGA,WAAS,WAAW,GAAG;AACrB,UAAM,OAAO,OAAO;AACpB,QAAI,KAAK,SAAS,KAAK,UAAU,GAAG;AAElC,cAAQ;AAAA,IACV,OAAO;AAEL,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,WAAS,eAAe;AACtB,UAAM,kBAAkB,OAAO,OAAO,mBAAmB;AAAA,MACvD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,UAAM,4BAA4B,OAAO,OAAO,mBAAmB;AAAA,MACjE,SAAS;AAAA,MACT,SAAS;AAAA,IACX,IAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAGA,WAAS,SAAS;AAChB,UAAM,OAAO,OAAO;AACpB,QAAI,KAAK,QAAS;AAClB,SAAK,UAAU;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AAGjB,WAAO,UAAU,iBAAiB,eAAe,gBAAgB,eAAe;AAChF,WAAO,UAAU,iBAAiB,eAAe,iBAAiB,yBAAyB;AAC3F,KAAC,aAAa,iBAAiB,YAAY,EAAE,QAAQ,eAAa;AAChE,aAAO,UAAU,iBAAiB,WAAW,cAAc,eAAe;AAAA,IAC5E,CAAC;AAGD,WAAO,UAAU,iBAAiB,eAAe,aAAa,yBAAyB;AAAA,EACzF;AACA,WAAS,UAAU;AACjB,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC,KAAK,QAAS;AACnB,SAAK,UAAU;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AAGjB,WAAO,UAAU,oBAAoB,eAAe,gBAAgB,eAAe;AACnF,WAAO,UAAU,oBAAoB,eAAe,iBAAiB,yBAAyB;AAC9F,KAAC,aAAa,iBAAiB,YAAY,EAAE,QAAQ,eAAa;AAChE,aAAO,UAAU,oBAAoB,WAAW,cAAc,eAAe;AAAA,IAC/E,CAAC;AAGD,WAAO,UAAU,oBAAoB,eAAe,aAAa,yBAAyB;AAAA,EAC5F;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,KAAK,SAAS;AAC9B,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,YAAQ;AAAA,EACV,CAAC;AACD,KAAG,cAAc,CAAC,IAAI,MAAM;AAC1B,QAAI,CAAC,OAAO,KAAK,QAAS;AAC1B,iBAAa,CAAC;AAAA,EAChB,CAAC;AACD,KAAG,YAAY,CAAC,IAAI,MAAM;AACxB,QAAI,CAAC,OAAO,KAAK,QAAS;AAC1B,eAAW;AAAA,EACb,CAAC;AACD,KAAG,aAAa,CAAC,IAAI,MAAM;AACzB,QAAI,CAAC,OAAO,aAAa,OAAO,OAAO,KAAK,WAAW,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,QAAQ;AACvG,iBAAW,CAAC;AAAA,IACd;AAAA,EACF,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,SAAS;AACrD,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,WAAW,OAAO,OAAO,SAAS;AAC9E,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACH;;;ACvrBA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,IAAI;AAAA;AAAA,IACN;AAAA,EACF,CAAC;AAED,SAAO,aAAa;AAAA,IAClB,SAAS;AAAA,EACX;AACA,WAAS,aAAa,GAAG,GAAG;AAC1B,UAAM,eAAe,yBAAS,SAAS;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,aAAO,CAAC,OAAO,QAAQ;AACrB,mBAAW;AACX,mBAAW,MAAM;AACjB,eAAO,WAAW,WAAW,GAAG;AAC9B,kBAAQ,WAAW,YAAY;AAC/B,cAAI,MAAM,KAAK,KAAK,KAAK;AACvB,uBAAW;AAAA,UACb,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,EAAE;AACF,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,YAAY,EAAE,SAAS;AAI5B,QAAI;AACJ,QAAI;AACJ,SAAK,cAAc,SAAS,YAAY,IAAI;AAC1C,UAAI,CAAC,GAAI,QAAO;AAGhB,WAAK,aAAa,KAAK,GAAG,EAAE;AAC5B,WAAK,KAAK;AAIV,cAAQ,KAAK,KAAK,EAAE,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,MAAM,KAAK,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,EAAE;AAAA,IAC9F;AACA,WAAO;AAAA,EACT;AACA,WAAS,uBAAuB,GAAG;AACjC,WAAO,WAAW,SAAS,OAAO,OAAO,OAAO,IAAI,aAAa,OAAO,YAAY,EAAE,UAAU,IAAI,IAAI,aAAa,OAAO,UAAU,EAAE,QAAQ;AAAA,EAClJ;AACA,WAAS,aAAa,IAAI,cAAc;AACtC,UAAM,aAAa,OAAO,WAAW;AACrC,QAAI;AACJ,QAAI;AACJ,UAAM,SAAS,OAAO;AACtB,aAAS,uBAAuB,GAAG;AACjC,UAAI,EAAE,UAAW;AAMjB,YAAM,YAAY,OAAO,eAAe,CAAC,OAAO,YAAY,OAAO;AACnE,UAAI,OAAO,OAAO,WAAW,OAAO,SAAS;AAC3C,+BAAuB,CAAC;AAGxB,8BAAsB,CAAC,OAAO,WAAW,OAAO,YAAY,CAAC,SAAS;AAAA,MACxE;AACA,UAAI,CAAC,uBAAuB,OAAO,OAAO,WAAW,OAAO,aAAa;AACvE,sBAAc,EAAE,aAAa,IAAI,EAAE,aAAa,MAAM,OAAO,aAAa,IAAI,OAAO,aAAa;AAClG,YAAI,OAAO,MAAM,UAAU,KAAK,CAAC,OAAO,SAAS,UAAU,GAAG;AAC5D,uBAAa;AAAA,QACf;AACA,+BAAuB,YAAY,OAAO,aAAa,KAAK,aAAa,EAAE,aAAa;AAAA,MAC1F;AACA,UAAI,OAAO,OAAO,WAAW,SAAS;AACpC,8BAAsB,EAAE,aAAa,IAAI;AAAA,MAC3C;AACA,QAAE,eAAe,mBAAmB;AACpC,QAAE,aAAa,qBAAqB,MAAM;AAC1C,QAAE,kBAAkB;AACpB,QAAE,oBAAoB;AAAA,IACxB;AACA,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,YAAI,WAAW,CAAC,MAAM,gBAAgB,WAAW,CAAC,aAAa,QAAQ;AACrE,iCAAuB,WAAW,CAAC,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF,WAAW,sBAAsB,UAAU,iBAAiB,YAAY;AACtE,6BAAuB,UAAU;AAAA,IACnC;AAAA,EACF;AACA,WAAS,cAAc,UAAU,cAAc;AAC7C,UAAM,SAAS,OAAO;AACtB,UAAM,aAAa,OAAO,WAAW;AACrC,QAAI;AACJ,aAAS,wBAAwB,GAAG;AAClC,UAAI,EAAE,UAAW;AACjB,QAAE,cAAc,UAAU,MAAM;AAChC,UAAI,aAAa,GAAG;AAClB,UAAE,gBAAgB;AAClB,YAAI,EAAE,OAAO,YAAY;AACvB,mBAAS,MAAM;AACb,cAAE,iBAAiB;AAAA,UACrB,CAAC;AAAA,QACH;AACA,6BAAqB,EAAE,WAAW,MAAM;AACtC,cAAI,CAAC,WAAY;AACjB,YAAE,cAAc;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,WAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,YAAI,WAAW,CAAC,MAAM,gBAAgB,WAAW,CAAC,aAAa,QAAQ;AACrE,kCAAwB,WAAW,CAAC,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF,WAAW,sBAAsB,UAAU,iBAAiB,YAAY;AACtE,8BAAwB,UAAU;AAAA,IACpC;AAAA,EACF;AACA,WAAS,eAAe;AACtB,QAAI,CAAC,OAAO,WAAW,QAAS;AAChC,QAAI,OAAO,WAAW,QAAQ;AAC5B,aAAO,WAAW,SAAS;AAC3B,aAAO,OAAO,WAAW;AAAA,IAC3B;AAAA,EACF;AACA,KAAG,cAAc,MAAM;AACrB,QAAI,OAAO,WAAW;AAAA,KAEtB,OAAO,OAAO,OAAO,WAAW,YAAY,YAAY,OAAO,OAAO,WAAW,mBAAmB,cAAc;AAChH,YAAM,kBAAkB,OAAO,OAAO,OAAO,WAAW,YAAY,WAAW,CAAC,GAAG,SAAS,iBAAiB,OAAO,OAAO,WAAW,OAAO,CAAC,IAAI,CAAC,OAAO,OAAO,WAAW,OAAO;AACnL,sBAAgB,QAAQ,oBAAkB;AACxC,YAAI,CAAC,OAAO,WAAW,QAAS,QAAO,WAAW,UAAU,CAAC;AAC7D,YAAI,kBAAkB,eAAe,QAAQ;AAC3C,iBAAO,WAAW,QAAQ,KAAK,eAAe,MAAM;AAAA,QACtD,WAAW,gBAAgB;AACzB,gBAAM,YAAY,GAAG,OAAO,OAAO,YAAY;AAC/C,gBAAM,qBAAqB,OAAK;AAC9B,mBAAO,WAAW,QAAQ,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1C,mBAAO,OAAO;AACd,2BAAe,oBAAoB,WAAW,kBAAkB;AAAA,UAClE;AACA,yBAAe,iBAAiB,WAAW,kBAAkB;AAAA,QAC/D;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,WAAO,WAAW,UAAU,OAAO,OAAO,WAAW;AAAA,EACvD,CAAC;AACD,KAAG,UAAU,MAAM;AACjB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,UAAU,MAAM;AACjB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,kBAAkB,MAAM;AACzB,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,gBAAgB,CAAC,IAAI,WAAW,iBAAiB;AAClD,QAAI,CAAC,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ,UAAW;AACvE,WAAO,WAAW,aAAa,WAAW,YAAY;AAAA,EACxD,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,UAAU,iBAAiB;AAClD,QAAI,CAAC,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ,UAAW;AACvE,WAAO,WAAW,cAAc,UAAU,YAAY;AAAA,EACxD,CAAC;AACD,SAAO,OAAO,OAAO,YAAY;AAAA,IAC/B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACxLA,SAAS,KAAK,MAAM;AAClB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,iCAAiC;AAAA,MACjC,eAAe;AAAA,MACf,4BAA4B;AAAA,MAC5B,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO,OAAO;AAAA,IACZ,SAAS;AAAA,EACX;AACA,MAAI,aAAa;AACjB,MAAI;AACJ,MAAI;AACJ,MAAI,8BAA6B,oBAAI,KAAK,GAAE,QAAQ;AACpD,WAAS,OAAO,SAAS;AACvB,UAAM,eAAe;AACrB,QAAI,aAAa,WAAW,EAAG;AAC/B,iBAAa,cAAc,OAAO;AAAA,EACpC;AACA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,MAAM,KAAK,MAAM,KAAK,KAAK,OAAO,CAAC,EAAE,SAAS,EAAE;AACnE,WAAO,IAAI,OAAO,IAAI,EAAE,QAAQ,MAAM,UAAU;AAAA,EAClD;AACA,WAAS,gBAAgB,IAAI;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,YAAY,GAAG;AAAA,IACpC,CAAC;AAAA,EACH;AACA,WAAS,mBAAmB,IAAI;AAC9B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,YAAY,IAAI;AAAA,IACrC,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI,MAAM;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,QAAQ,IAAI;AAAA,IACjC,CAAC;AAAA,EACH;AACA,WAAS,qBAAqB,IAAI,aAAa;AAC7C,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,wBAAwB,WAAW;AAAA,IACxD,CAAC;AAAA,EACH;AACA,WAAS,cAAc,IAAI,UAAU;AACnC,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,QAAQ;AAAA,IAC9C,CAAC;AAAA,EACH;AACA,WAAS,WAAW,IAAI,OAAO;AAC7B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,cAAc,KAAK;AAAA,IACxC,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,IAAI,IAAI;AACvB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,MAAM,EAAE;AAAA,IAC7B,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI,MAAM;AAC3B,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AACA,WAAS,UAAU,IAAI;AACrB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,IAAI;AAAA,IAC1C,CAAC;AAAA,EACH;AACA,WAAS,SAAS,IAAI;AACpB,SAAK,kBAAkB,EAAE;AACzB,OAAG,QAAQ,WAAS;AAClB,YAAM,aAAa,iBAAiB,KAAK;AAAA,IAC3C,CAAC;AAAA,EACH;AACA,WAAS,kBAAkB,GAAG;AAC5B,QAAI,EAAE,YAAY,MAAM,EAAE,YAAY,GAAI;AAC1C,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,WAAW,EAAE;AACnB,QAAI,OAAO,cAAc,OAAO,WAAW,OAAO,aAAa,OAAO,WAAW,MAAM,OAAO,WAAW,GAAG,SAAS,EAAE,MAAM,IAAI;AAC/H,UAAI,CAAC,EAAE,OAAO,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC,EAAG;AAAA,IAClF;AACA,QAAI,OAAO,cAAc,OAAO,WAAW,UAAU,OAAO,WAAW,QAAQ;AAC7E,YAAM,UAAU,kBAAkB,OAAO,WAAW,MAAM;AAC1D,YAAM,UAAU,kBAAkB,OAAO,WAAW,MAAM;AAC1D,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAC9B,YAAI,EAAE,OAAO,SAAS,CAAC,OAAO,OAAO,OAAO;AAC1C,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,OAAO,OAAO;AAChB,iBAAO,OAAO,gBAAgB;AAAA,QAChC,OAAO;AACL,iBAAO,OAAO,gBAAgB;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAC9B,YAAI,EAAE,OAAO,eAAe,CAAC,OAAO,OAAO,OAAO;AAChD,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,OAAO,aAAa;AACtB,iBAAO,OAAO,iBAAiB;AAAA,QACjC,OAAO;AACL,iBAAO,OAAO,gBAAgB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,cAAc,SAAS,QAAQ,kBAAkB,OAAO,OAAO,WAAW,WAAW,CAAC,GAAG;AAClG,eAAS,MAAM;AAAA,IACjB;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,OAAO,OAAO,QAAQ,OAAO,OAAO,UAAU,CAAC,OAAO,WAAY;AACtE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,QAAI,QAAQ;AACV,UAAI,OAAO,aAAa;AACtB,kBAAU,MAAM;AAChB,2BAAmB,MAAM;AAAA,MAC3B,OAAO;AACL,iBAAS,MAAM;AACf,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,UAAI,OAAO,OAAO;AAChB,kBAAU,MAAM;AAChB,2BAAmB,MAAM;AAAA,MAC3B,OAAO;AACL,iBAAS,MAAM;AACf,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,WAAS,gBAAgB;AACvB,WAAO,OAAO,cAAc,OAAO,WAAW,WAAW,OAAO,WAAW,QAAQ;AAAA,EACrF;AACA,WAAS,yBAAyB;AAChC,WAAO,cAAc,KAAK,OAAO,OAAO,WAAW;AAAA,EACrD;AACA,WAAS,mBAAmB;AAC1B,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,CAAC,cAAc,EAAG;AACtB,WAAO,WAAW,QAAQ,QAAQ,cAAY;AAC5C,UAAI,OAAO,OAAO,WAAW,WAAW;AACtC,wBAAgB,QAAQ;AACxB,YAAI,CAAC,OAAO,OAAO,WAAW,cAAc;AAC1C,oBAAU,UAAU,QAAQ;AAC5B,qBAAW,UAAU,OAAO,wBAAwB,QAAQ,iBAAiB,aAAa,QAAQ,IAAI,CAAC,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,SAAS,QAAQ,kBAAkB,OAAO,OAAO,WAAW,iBAAiB,CAAC,GAAG;AACnF,iBAAS,aAAa,gBAAgB,MAAM;AAAA,MAC9C,OAAO;AACL,iBAAS,gBAAgB,cAAc;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,YAAY,CAAC,IAAI,WAAW,YAAY;AAC5C,oBAAgB,EAAE;AAClB,QAAI,GAAG,YAAY,UAAU;AAC3B,gBAAU,IAAI,QAAQ;AACtB,SAAG,iBAAiB,WAAW,iBAAiB;AAAA,IAClD;AACA,eAAW,IAAI,OAAO;AACtB,kBAAc,IAAI,SAAS;AAAA,EAC7B;AACA,QAAM,oBAAoB,OAAK;AAC7B,QAAI,sBAAsB,uBAAuB,EAAE,UAAU,CAAC,mBAAmB,SAAS,EAAE,MAAM,GAAG;AACnG,4BAAsB;AAAA,IACxB;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,QAAM,kBAAkB,MAAM;AAC5B,0BAAsB;AACtB,0BAAsB,MAAM;AAC1B,4BAAsB,MAAM;AAC1B,YAAI,CAAC,OAAO,WAAW;AACrB,iBAAO,KAAK,UAAU;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,qBAAqB,OAAK;AAC9B,kCAA6B,oBAAI,KAAK,GAAE,QAAQ;AAAA,EAClD;AACA,QAAM,cAAc,OAAK;AACvB,QAAI,OAAO,KAAK,WAAW,CAAC,OAAO,OAAO,KAAK,cAAe;AAC9D,SAAI,oBAAI,KAAK,GAAE,QAAQ,IAAI,6BAA6B,IAAK;AAC7D,UAAM,UAAU,EAAE,OAAO,QAAQ,IAAI,OAAO,OAAO,UAAU,gBAAgB;AAC7E,QAAI,CAAC,WAAW,CAAC,OAAO,OAAO,SAAS,OAAO,EAAG;AAClD,yBAAqB;AACrB,UAAM,WAAW,OAAO,OAAO,QAAQ,OAAO,MAAM,OAAO;AAC3D,UAAM,YAAY,OAAO,OAAO,uBAAuB,OAAO,iBAAiB,OAAO,cAAc,SAAS,OAAO;AACpH,QAAI,YAAY,UAAW;AAC3B,QAAI,EAAE,sBAAsB,EAAE,mBAAmB,iBAAkB;AACnE,QAAI,OAAO,aAAa,GAAG;AACzB,aAAO,GAAG,aAAa;AAAA,IACzB,OAAO;AACL,aAAO,GAAG,YAAY;AAAA,IACxB;AACA,0BAAsB,MAAM;AAC1B,UAAI,oBAAqB;AACzB,UAAI,OAAO,OAAO,MAAM;AACtB,eAAO,YAAY,SAAS,QAAQ,aAAa,yBAAyB,CAAC,GAAG,CAAC;AAAA,MACjF,OAAO;AACL,eAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AACA,4BAAsB;AAAA,IACxB,CAAC;AAAA,EACH;AACA,QAAM,aAAa,MAAM;AACvB,UAAM,SAAS,OAAO,OAAO;AAC7B,QAAI,OAAO,4BAA4B;AACrC,2BAAqB,OAAO,QAAQ,OAAO,0BAA0B;AAAA,IACvE;AACA,QAAI,OAAO,WAAW;AACpB,gBAAU,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC3C;AACA,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,OAAO,mBAAmB;AAC5B,aAAO,OAAO,QAAQ,CAAC,SAAS,UAAU;AACxC,cAAM,aAAa,OAAO,OAAO,OAAO,SAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE,IAAI;AACxG,cAAM,mBAAmB,OAAO,kBAAkB,QAAQ,iBAAiB,aAAa,CAAC,EAAE,QAAQ,wBAAwB,YAAY;AACvI,mBAAW,SAAS,gBAAgB;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,UAAM,SAAS,OAAO,OAAO;AAC7B,WAAO,GAAG,OAAO,UAAU;AAG3B,UAAM,cAAc,OAAO;AAC3B,QAAI,OAAO,iCAAiC;AAC1C,2BAAqB,aAAa,OAAO,+BAA+B;AAAA,IAC1E;AACA,QAAI,OAAO,kBAAkB;AAC3B,iBAAW,aAAa,OAAO,gBAAgB;AAAA,IACjD;AACA,QAAI,OAAO,eAAe;AACxB,gBAAU,aAAa,OAAO,aAAa;AAAA,IAC7C;AAGA,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,OAAO,MAAM,UAAU,aAAa,IAAI,KAAK,kBAAkB,gBAAgB,EAAE,CAAC;AACpG,UAAM,OAAO,OAAO,OAAO,YAAY,OAAO,OAAO,SAAS,UAAU,QAAQ;AAChF,YAAQ,WAAW,SAAS;AAC5B,cAAU,WAAW,IAAI;AAGzB,eAAW;AAGX,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO,aAAa,OAAO,aAAa,CAAC;AAC7C,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,UAAU,IAAI,WAAW,OAAO,gBAAgB,CAAC;AAAA,IACxE;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,UAAU,IAAI,WAAW,OAAO,gBAAgB,CAAC;AAAA,IACxE;AAGA,QAAI,uBAAuB,GAAG;AAC5B,YAAM,eAAe,kBAAkB,OAAO,WAAW,EAAE;AAC3D,mBAAa,QAAQ,QAAM;AACzB,WAAG,iBAAiB,WAAW,iBAAiB;AAAA,MAClD,CAAC;AAAA,IACH;AAGA,UAAME,YAAW,YAAY;AAC7B,IAAAA,UAAS,iBAAiB,oBAAoB,kBAAkB;AAChE,WAAO,GAAG,iBAAiB,SAAS,aAAa,IAAI;AACrD,WAAO,GAAG,iBAAiB,SAAS,aAAa,IAAI;AACrD,WAAO,GAAG,iBAAiB,eAAe,mBAAmB,IAAI;AACjE,WAAO,GAAG,iBAAiB,aAAa,iBAAiB,IAAI;AAAA,EAC/D;AACA,WAAS,UAAU;AACjB,QAAI,WAAY,YAAW,OAAO;AAClC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,OAAO,aAAa,OAAO,aAAa,CAAC;AAC7C,aAAS,kBAAkB,MAAM;AACjC,aAAS,kBAAkB,MAAM;AACjC,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,GAAG,oBAAoB,WAAW,iBAAiB,CAAC;AAAA,IAC3E;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,QAAM,GAAG,oBAAoB,WAAW,iBAAiB,CAAC;AAAA,IAC3E;AAGA,QAAI,uBAAuB,GAAG;AAC5B,YAAM,eAAe,kBAAkB,OAAO,WAAW,EAAE;AAC3D,mBAAa,QAAQ,QAAM;AACzB,WAAG,oBAAoB,WAAW,iBAAiB;AAAA,MACrD,CAAC;AAAA,IACH;AACA,UAAMA,YAAW,YAAY;AAC7B,IAAAA,UAAS,oBAAoB,oBAAoB,kBAAkB;AAEnE,QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,UAAU;AAC9C,aAAO,GAAG,oBAAoB,SAAS,aAAa,IAAI;AACxD,aAAO,GAAG,oBAAoB,eAAe,mBAAmB,IAAI;AACpE,aAAO,GAAG,oBAAoB,aAAa,iBAAiB,IAAI;AAAA,IAClE;AAAA,EACF;AACA,KAAG,cAAc,MAAM;AACrB,iBAAa,cAAc,QAAQ,OAAO,OAAO,KAAK,iBAAiB;AACvE,eAAW,aAAa,aAAa,WAAW;AAChD,eAAW,aAAa,eAAe,MAAM;AAAA,EAC/C,CAAC;AACD,KAAG,aAAa,MAAM;AACpB,QAAI,CAAC,OAAO,OAAO,KAAK,QAAS;AACjC,SAAK;AAAA,EACP,CAAC;AACD,KAAG,kEAAkE,MAAM;AACzE,QAAI,CAAC,OAAO,OAAO,KAAK,QAAS;AACjC,eAAW;AAAA,EACb,CAAC;AACD,KAAG,yCAAyC,MAAM;AAChD,QAAI,CAAC,OAAO,OAAO,KAAK,QAAS;AACjC,qBAAiB;AAAA,EACnB,CAAC;AACD,KAAG,oBAAoB,MAAM;AAC3B,QAAI,CAAC,OAAO,OAAO,KAAK,QAAS;AACjC,qBAAiB;AAAA,EACnB,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,CAAC,OAAO,OAAO,KAAK,QAAS;AACjC,YAAQ;AAAA,EACV,CAAC;AACH;;;ACpXA,SAAS,QAAQ,MAAM;AACrB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,cAAc;AAAA,MACd,KAAK;AAAA,MACL,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,cAAc;AAClB,MAAI,QAAQ,CAAC;AACb,QAAM,UAAU,UAAQ;AACtB,WAAO,KAAK,SAAS,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,YAAY,EAAE,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAAA,EAC/H;AACA,QAAM,gBAAgB,iBAAe;AACnC,UAAMC,UAAS,UAAU;AACzB,QAAI;AACJ,QAAI,aAAa;AACf,iBAAW,IAAI,IAAI,WAAW;AAAA,IAChC,OAAO;AACL,iBAAWA,QAAO;AAAA,IACpB;AACA,UAAM,YAAY,SAAS,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,OAAO,UAAQ,SAAS,EAAE;AAClF,UAAM,QAAQ,UAAU;AACxB,UAAM,MAAM,UAAU,QAAQ,CAAC;AAC/B,UAAM,QAAQ,UAAU,QAAQ,CAAC;AACjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC,KAAK,UAAU;AACjC,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,eAAe,CAAC,OAAO,OAAO,QAAQ,QAAS;AACpD,QAAI;AACJ,QAAI,OAAO,OAAO,KAAK;AACrB,iBAAW,IAAI,IAAI,OAAO,OAAO,GAAG;AAAA,IACtC,OAAO;AACL,iBAAWA,QAAO;AAAA,IACpB;AACA,UAAM,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,SAAS,cAAc,6BAA6B,KAAK,IAAI,IAAI,OAAO,OAAO,KAAK;AAC3J,QAAI,QAAQ,QAAQ,MAAM,aAAa,cAAc,CAAC;AACtD,QAAI,OAAO,OAAO,QAAQ,KAAK,SAAS,GAAG;AACzC,UAAI,OAAO,OAAO,OAAO,QAAQ;AACjC,UAAI,KAAK,KAAK,SAAS,CAAC,MAAM,IAAK,QAAO,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AACvE,cAAQ,GAAG,IAAI,IAAI,MAAM,GAAG,GAAG,MAAM,EAAE,GAAG,KAAK;AAAA,IACjD,WAAW,CAAC,SAAS,SAAS,SAAS,GAAG,GAAG;AAC3C,cAAQ,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE,GAAG,KAAK;AAAA,IACzC;AACA,QAAI,OAAO,OAAO,QAAQ,WAAW;AACnC,eAAS,SAAS;AAAA,IACpB;AACA,UAAM,eAAeA,QAAO,QAAQ;AACpC,QAAI,gBAAgB,aAAa,UAAU,OAAO;AAChD;AAAA,IACF;AACA,QAAI,OAAO,OAAO,QAAQ,cAAc;AACtC,MAAAA,QAAO,QAAQ,aAAa;AAAA,QAC1B;AAAA,MACF,GAAG,MAAM,KAAK;AAAA,IAChB,OAAO;AACL,MAAAA,QAAO,QAAQ,UAAU;AAAA,QACvB;AAAA,MACF,GAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC,OAAO,OAAO,iBAAiB;AACpD,QAAI,OAAO;AACT,eAAS,IAAI,GAAG,SAAS,OAAO,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACjE,cAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,cAAM,eAAe,QAAQ,MAAM,aAAa,cAAc,CAAC;AAC/D,YAAI,iBAAiB,OAAO;AAC1B,gBAAM,QAAQ,OAAO,cAAc,KAAK;AACxC,iBAAO,QAAQ,OAAO,OAAO,YAAY;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,GAAG,OAAO,YAAY;AAAA,IACvC;AAAA,EACF;AACA,QAAM,qBAAqB,MAAM;AAC/B,YAAQ,cAAc,OAAO,OAAO,GAAG;AACvC,kBAAc,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAAA,EACvD;AACA,QAAM,OAAO,MAAM;AACjB,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,OAAO,OAAO,QAAS;AAC5B,QAAI,CAACA,QAAO,WAAW,CAACA,QAAO,QAAQ,WAAW;AAChD,aAAO,OAAO,QAAQ,UAAU;AAChC,aAAO,OAAO,eAAe,UAAU;AACvC;AAAA,IACF;AACA,kBAAc;AACd,YAAQ,cAAc,OAAO,OAAO,GAAG;AACvC,QAAI,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;AAC9B,UAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,QAAAA,QAAO,iBAAiB,YAAY,kBAAkB;AAAA,MACxD;AACA;AAAA,IACF;AACA,kBAAc,GAAG,MAAM,OAAO,OAAO,OAAO,kBAAkB;AAC9D,QAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,MAAAA,QAAO,iBAAiB,YAAY,kBAAkB;AAAA,IACxD;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAMA,UAAS,UAAU;AACzB,QAAI,CAAC,OAAO,OAAO,QAAQ,cAAc;AACvC,MAAAA,QAAO,oBAAoB,YAAY,kBAAkB;AAAA,IAC3D;AAAA,EACF;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,QAAQ,SAAS;AACjC,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,QAAQ,SAAS;AACjC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,QAAI,aAAa;AACf,iBAAW,OAAO,OAAO,QAAQ,KAAK,OAAO,WAAW;AAAA,IAC1D;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,eAAe,OAAO,OAAO,SAAS;AACxC,iBAAW,OAAO,OAAO,QAAQ,KAAK,OAAO,WAAW;AAAA,IAC1D;AAAA,EACF,CAAC;AACH;;;ACxIA,SAAS,eAAe,MAAM;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,cAAc;AAClB,QAAMC,YAAW,YAAY;AAC7B,QAAMC,UAAS,UAAU;AACzB,eAAa;AAAA,IACX,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc,IAAI,MAAM;AACtB,YAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACnD,gBAAM,gBAAgB,OAAO,OAAO,KAAK,aAAW,QAAQ,aAAa,WAAW,MAAM,IAAI;AAC9F,cAAI,CAAC,cAAe,QAAO;AAC3B,gBAAM,QAAQ,SAAS,cAAc,aAAa,yBAAyB,GAAG,EAAE;AAChF,iBAAO;AAAA,QACT;AACA,eAAO,OAAO,cAAc,gBAAgB,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,eAAe,IAAI,+BAA+B,IAAI,IAAI,EAAE,CAAC,CAAC;AAAA,MACzJ;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,SAAK,YAAY;AACjB,UAAM,UAAUD,UAAS,SAAS,KAAK,QAAQ,KAAK,EAAE;AACtD,UAAM,gBAAgB,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,SAAS,cAAc,6BAA6B,OAAO,WAAW,IAAI,IAAI,OAAO,OAAO,OAAO,WAAW;AAC7L,UAAM,kBAAkB,gBAAgB,cAAc,aAAa,WAAW,IAAI;AAClF,QAAI,YAAY,iBAAiB;AAC/B,YAAM,WAAW,OAAO,OAAO,eAAe,cAAc,QAAQ,OAAO;AAC3E,UAAI,OAAO,aAAa,eAAe,OAAO,MAAM,QAAQ,EAAG;AAC/D,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC,eAAe,CAAC,OAAO,OAAO,eAAe,QAAS;AAC3D,UAAM,gBAAgB,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,SAAS,cAAc,6BAA6B,OAAO,WAAW,IAAI,IAAI,OAAO,OAAO,OAAO,WAAW;AAC7L,UAAM,kBAAkB,gBAAgB,cAAc,aAAa,WAAW,KAAK,cAAc,aAAa,cAAc,IAAI;AAChI,QAAI,OAAO,OAAO,eAAe,gBAAgBC,QAAO,WAAWA,QAAO,QAAQ,cAAc;AAC9F,MAAAA,QAAO,QAAQ,aAAa,MAAM,MAAM,IAAI,eAAe,MAAM,EAAE;AACnE,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,MAAAD,UAAS,SAAS,OAAO,mBAAmB;AAC5C,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,OAAO,OAAO,eAAe,WAAW,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,QAAS;AACrG,kBAAc;AACd,UAAM,OAAOA,UAAS,SAAS,KAAK,QAAQ,KAAK,EAAE;AACnD,QAAI,MAAM;AACR,YAAM,QAAQ;AACd,YAAM,QAAQ,OAAO,OAAO,eAAe,cAAc,QAAQ,IAAI;AACrE,aAAO,QAAQ,SAAS,GAAG,OAAO,OAAO,OAAO,oBAAoB,IAAI;AAAA,IAC1E;AACA,QAAI,OAAO,OAAO,eAAe,YAAY;AAC3C,MAAAC,QAAO,iBAAiB,cAAc,YAAY;AAAA,IACpD;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,OAAO,OAAO,eAAe,YAAY;AAC3C,MAAAA,QAAO,oBAAoB,cAAc,YAAY;AAAA,IACvD;AAAA,EACF;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,eAAe,SAAS;AACxC,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,QAAI,OAAO,OAAO,eAAe,SAAS;AACxC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,QAAI,aAAa;AACf,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,eAAe,OAAO,OAAO,SAAS;AACxC,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;;;ACtFA,SAAS,SAAS,MAAM;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,WAAW;AAAA,IAChB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACA,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB,UAAU,OAAO,WAAW,OAAO,SAAS,QAAQ;AAC7E,MAAI,uBAAuB,UAAU,OAAO,WAAW,OAAO,SAAS,QAAQ;AAC/E,MAAI;AACJ,MAAI,qBAAoB,oBAAI,KAAK,GAAE,QAAQ;AAC3C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,WAAS,gBAAgB,GAAG;AAC1B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,UAAW;AACtD,QAAI,EAAE,WAAW,OAAO,UAAW;AACnC,WAAO,UAAU,oBAAoB,iBAAiB,eAAe;AACrE,QAAI,wBAAwB,EAAE,UAAU,EAAE,OAAO,mBAAmB;AAClE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,MAAM;AACzB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,QAAI,OAAO,SAAS,QAAQ;AAC1B,kBAAY;AAAA,IACd,WAAW,WAAW;AACpB,6BAAuB;AACvB,kBAAY;AAAA,IACd;AACA,UAAM,WAAW,OAAO,SAAS,SAAS,mBAAmB,oBAAoB,wBAAuB,oBAAI,KAAK,GAAE,QAAQ;AAC3H,WAAO,SAAS,WAAW;AAC3B,SAAK,oBAAoB,UAAU,WAAW,kBAAkB;AAChE,UAAM,sBAAsB,MAAM;AAChC,mBAAa;AAAA,IACf,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,MAAM;AAC1B,QAAI;AACJ,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACnD,sBAAgB,OAAO,OAAO,KAAK,aAAW,QAAQ,UAAU,SAAS,qBAAqB,CAAC;AAAA,IACjG,OAAO;AACL,sBAAgB,OAAO,OAAO,OAAO,WAAW;AAAA,IAClD;AACA,QAAI,CAAC,cAAe,QAAO;AAC3B,UAAM,oBAAoB,SAAS,cAAc,aAAa,sBAAsB,GAAG,EAAE;AACzF,WAAO;AAAA,EACT;AACA,QAAM,MAAM,gBAAc;AACxB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,yBAAqB,GAAG;AACxB,iBAAa;AACb,QAAI,QAAQ,OAAO,eAAe,cAAc,OAAO,OAAO,SAAS,QAAQ;AAC/E,yBAAqB,OAAO,OAAO,SAAS;AAC5C,2BAAuB,OAAO,OAAO,SAAS;AAC9C,UAAM,oBAAoB,cAAc;AACxC,QAAI,CAAC,OAAO,MAAM,iBAAiB,KAAK,oBAAoB,KAAK,OAAO,eAAe,aAAa;AAClG,cAAQ;AACR,2BAAqB;AACrB,6BAAuB;AAAA,IACzB;AACA,uBAAmB;AACnB,UAAM,QAAQ,OAAO,OAAO;AAC5B,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,UAAU,OAAO,UAAW;AACjC,UAAI,OAAO,OAAO,SAAS,kBAAkB;AAC3C,YAAI,CAAC,OAAO,eAAe,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ;AACrE,iBAAO,UAAU,OAAO,MAAM,IAAI;AAClC,eAAK,UAAU;AAAA,QACjB,WAAW,CAAC,OAAO,OAAO,SAAS,iBAAiB;AAClD,iBAAO,QAAQ,OAAO,OAAO,SAAS,GAAG,OAAO,MAAM,IAAI;AAC1D,eAAK,UAAU;AAAA,QACjB;AAAA,MACF,OAAO;AACL,YAAI,CAAC,OAAO,SAAS,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ;AAC/D,iBAAO,UAAU,OAAO,MAAM,IAAI;AAClC,eAAK,UAAU;AAAA,QACjB,WAAW,CAAC,OAAO,OAAO,SAAS,iBAAiB;AAClD,iBAAO,QAAQ,GAAG,OAAO,MAAM,IAAI;AACnC,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AACA,UAAI,OAAO,OAAO,SAAS;AACzB,6BAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,8BAAsB,MAAM;AAC1B,cAAI;AAAA,QACN,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,QAAQ,GAAG;AACb,mBAAa,OAAO;AACpB,gBAAU,WAAW,MAAM;AACzB,gBAAQ;AAAA,MACV,GAAG,KAAK;AAAA,IACV,OAAO;AACL,4BAAsB,MAAM;AAC1B,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAGA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM;AAClB,yBAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,WAAO,SAAS,UAAU;AAC1B,QAAI;AACJ,SAAK,eAAe;AAAA,EACtB;AACA,QAAM,OAAO,MAAM;AACjB,WAAO,SAAS,UAAU;AAC1B,iBAAa,OAAO;AACpB,yBAAqB,GAAG;AACxB,SAAK,cAAc;AAAA,EACrB;AACA,QAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,iBAAa,OAAO;AACpB,QAAI,CAAC,UAAU;AACb,4BAAsB;AAAA,IACxB;AACA,UAAM,UAAU,MAAM;AACpB,WAAK,eAAe;AACpB,UAAI,OAAO,OAAO,SAAS,mBAAmB;AAC5C,eAAO,UAAU,iBAAiB,iBAAiB,eAAe;AAAA,MACpE,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,SAAS;AACzB,QAAI,OAAO;AACT,UAAI,cAAc;AAChB,2BAAmB,OAAO,OAAO,SAAS;AAAA,MAC5C;AACA,qBAAe;AACf,cAAQ;AACR;AAAA,IACF;AACA,UAAM,QAAQ,oBAAoB,OAAO,OAAO,SAAS;AACzD,uBAAmB,UAAS,oBAAI,KAAK,GAAE,QAAQ,IAAI;AACnD,QAAI,OAAO,SAAS,mBAAmB,KAAK,CAAC,OAAO,OAAO,KAAM;AACjE,QAAI,mBAAmB,EAAG,oBAAmB;AAC7C,YAAQ;AAAA,EACV;AACA,QAAM,SAAS,MAAM;AACnB,QAAI,OAAO,SAAS,mBAAmB,KAAK,CAAC,OAAO,OAAO,QAAQ,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AACjH,yBAAoB,oBAAI,KAAK,GAAE,QAAQ;AACvC,QAAI,qBAAqB;AACvB,4BAAsB;AACtB,UAAI,gBAAgB;AAAA,IACtB,OAAO;AACL,UAAI;AAAA,IACN;AACA,WAAO,SAAS,SAAS;AACzB,SAAK,gBAAgB;AAAA,EACvB;AACA,QAAM,qBAAqB,MAAM;AAC/B,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,UAAMC,YAAW,YAAY;AAC7B,QAAIA,UAAS,oBAAoB,UAAU;AACzC,4BAAsB;AACtB,YAAM,IAAI;AAAA,IACZ;AACA,QAAIA,UAAS,oBAAoB,WAAW;AAC1C,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,iBAAiB,OAAK;AAC1B,QAAI,EAAE,gBAAgB,QAAS;AAC/B,0BAAsB;AACtB,2BAAuB;AACvB,QAAI,OAAO,aAAa,OAAO,SAAS,OAAQ;AAChD,UAAM,IAAI;AAAA,EACZ;AACA,QAAM,iBAAiB,OAAK;AAC1B,QAAI,EAAE,gBAAgB,QAAS;AAC/B,2BAAuB;AACvB,QAAI,OAAO,SAAS,QAAQ;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,OAAO,OAAO,SAAS,mBAAmB;AAC5C,aAAO,GAAG,iBAAiB,gBAAgB,cAAc;AACzD,aAAO,GAAG,iBAAiB,gBAAgB,cAAc;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,UAAU;AAC9C,aAAO,GAAG,oBAAoB,gBAAgB,cAAc;AAC5D,aAAO,GAAG,oBAAoB,gBAAgB,cAAc;AAAA,IAC9D;AAAA,EACF;AACA,QAAM,uBAAuB,MAAM;AACjC,UAAMA,YAAW,YAAY;AAC7B,IAAAA,UAAS,iBAAiB,oBAAoB,kBAAkB;AAAA,EAClE;AACA,QAAM,uBAAuB,MAAM;AACjC,UAAMA,YAAW,YAAY;AAC7B,IAAAA,UAAS,oBAAoB,oBAAoB,kBAAkB;AAAA,EACrE;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,SAAS,SAAS;AAClC,wBAAkB;AAClB,2BAAqB;AACrB,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,sBAAkB;AAClB,yBAAqB;AACrB,QAAI,OAAO,SAAS,SAAS;AAC3B,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,0BAA0B,MAAM;AACjC,QAAI,iBAAiB,qBAAqB;AACxC,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,8BAA8B,MAAM;AACrC,QAAI,CAAC,OAAO,OAAO,SAAS,sBAAsB;AAChD,YAAM,MAAM,IAAI;AAAA,IAClB,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,yBAAyB,CAAC,IAAI,OAAO,aAAa;AACnD,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,QAAI,YAAY,CAAC,OAAO,OAAO,SAAS,sBAAsB;AAC5D,YAAM,MAAM,IAAI;AAAA,IAClB,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF,CAAC;AACD,KAAG,mBAAmB,MAAM;AAC1B,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,QAAI,OAAO,OAAO,SAAS,sBAAsB;AAC/C,WAAK;AACL;AAAA,IACF;AACA,gBAAY;AACZ,oBAAgB;AAChB,0BAAsB;AACtB,wBAAoB,WAAW,MAAM;AACnC,4BAAsB;AACtB,sBAAgB;AAChB,YAAM,IAAI;AAAA,IACZ,GAAG,GAAG;AAAA,EACR,CAAC;AACD,KAAG,YAAY,MAAM;AACnB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,WAAW,CAAC,UAAW;AAChE,iBAAa,iBAAiB;AAC9B,iBAAa,OAAO;AACpB,QAAI,OAAO,OAAO,SAAS,sBAAsB;AAC/C,sBAAgB;AAChB,kBAAY;AACZ;AAAA,IACF;AACA,QAAI,iBAAiB,OAAO,OAAO,QAAS,QAAO;AACnD,oBAAgB;AAChB,gBAAY;AAAA,EACd,CAAC;AACD,KAAG,eAAe,MAAM;AACtB,QAAI,OAAO,aAAa,CAAC,OAAO,SAAS,QAAS;AAClD,mBAAe;AAAA,EACjB,CAAC;AACD,SAAO,OAAO,OAAO,UAAU;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AC1SA,SAAS,MAAM,MAAM;AACnB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,IACxB;AAAA,EACF,CAAC;AACD,MAAI,cAAc;AAClB,MAAI,gBAAgB;AACpB,SAAO,SAAS;AAAA,IACd,QAAQ;AAAA,EACV;AACA,WAAS,eAAe;AACtB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa,UAAW;AAC7C,UAAM,eAAe,aAAa;AAClC,UAAM,eAAe,aAAa;AAClC,QAAI,gBAAgB,aAAa,UAAU,SAAS,OAAO,OAAO,OAAO,qBAAqB,EAAG;AACjG,QAAI,OAAO,iBAAiB,eAAe,iBAAiB,KAAM;AAClE,QAAI;AACJ,QAAI,aAAa,OAAO,MAAM;AAC5B,qBAAe,SAAS,aAAa,aAAa,aAAa,yBAAyB,GAAG,EAAE;AAAA,IAC/F,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,YAAY,YAAY;AAAA,IACjC,OAAO;AACL,aAAO,QAAQ,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,OAAO;AACd,UAAM;AAAA,MACJ,QAAQ;AAAA,IACV,IAAI,OAAO;AACX,QAAI,YAAa,QAAO;AACxB,kBAAc;AACd,UAAM,cAAc,OAAO;AAC3B,QAAI,aAAa,kBAAkB,aAAa;AAC9C,UAAI,aAAa,OAAO,WAAW;AACjC,sBAAc;AACd,eAAO;AAAA,MACT;AACA,aAAO,OAAO,SAAS,aAAa;AACpC,aAAO,OAAO,OAAO,OAAO,OAAO,gBAAgB;AAAA,QACjD,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,OAAO,OAAO,OAAO,QAAQ;AAAA,QACzC,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,OAAO,OAAO;AAAA,IAC9B,WAAW,SAAS,aAAa,MAAM,GAAG;AACxC,YAAM,qBAAqB,OAAO,OAAO,CAAC,GAAG,aAAa,MAAM;AAChE,aAAO,OAAO,oBAAoB;AAAA,QAChC,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC;AACD,aAAO,OAAO,SAAS,IAAI,YAAY,kBAAkB;AACzD,sBAAgB;AAAA,IAClB;AACA,WAAO,OAAO,OAAO,GAAG,UAAU,IAAI,OAAO,OAAO,OAAO,oBAAoB;AAC/E,WAAO,OAAO,OAAO,GAAG,OAAO,YAAY;AAC3C,WAAO;AAAA,EACT;AACA,WAAS,OAAO,SAAS;AACvB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa,UAAW;AAC7C,UAAM,gBAAgB,aAAa,OAAO,kBAAkB,SAAS,aAAa,qBAAqB,IAAI,aAAa,OAAO;AAG/H,QAAI,mBAAmB;AACvB,UAAM,mBAAmB,OAAO,OAAO,OAAO;AAC9C,QAAI,OAAO,OAAO,gBAAgB,KAAK,CAAC,OAAO,OAAO,gBAAgB;AACpE,yBAAmB,OAAO,OAAO;AAAA,IACnC;AACA,QAAI,CAAC,OAAO,OAAO,OAAO,sBAAsB;AAC9C,yBAAmB;AAAA,IACrB;AACA,uBAAmB,KAAK,MAAM,gBAAgB;AAC9C,iBAAa,OAAO,QAAQ,aAAW,QAAQ,UAAU,OAAO,gBAAgB,CAAC;AACjF,QAAI,aAAa,OAAO,QAAQ,aAAa,OAAO,WAAW,aAAa,OAAO,QAAQ,SAAS;AAClG,eAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK,GAAG;AAC5C,wBAAgB,aAAa,UAAU,6BAA6B,OAAO,YAAY,CAAC,IAAI,EAAE,QAAQ,aAAW;AAC/G,kBAAQ,UAAU,IAAI,gBAAgB;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK,GAAG;AAC5C,YAAI,aAAa,OAAO,OAAO,YAAY,CAAC,GAAG;AAC7C,uBAAa,OAAO,OAAO,YAAY,CAAC,EAAE,UAAU,IAAI,gBAAgB;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AACA,UAAM,mBAAmB,OAAO,OAAO,OAAO;AAC9C,UAAM,YAAY,oBAAoB,CAAC,aAAa,OAAO;AAC3D,QAAI,OAAO,cAAc,aAAa,aAAa,WAAW;AAC5D,YAAM,qBAAqB,aAAa;AACxC,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa,OAAO,MAAM;AAC5B,cAAM,iBAAiB,aAAa,OAAO,KAAK,aAAW,QAAQ,aAAa,yBAAyB,MAAM,GAAG,OAAO,SAAS,EAAE;AACpI,yBAAiB,aAAa,OAAO,QAAQ,cAAc;AAC3D,oBAAY,OAAO,cAAc,OAAO,gBAAgB,SAAS;AAAA,MACnE,OAAO;AACL,yBAAiB,OAAO;AACxB,oBAAY,iBAAiB,OAAO,gBAAgB,SAAS;AAAA,MAC/D;AACA,UAAI,WAAW;AACb,0BAAkB,cAAc,SAAS,mBAAmB,KAAK;AAAA,MACnE;AACA,UAAI,aAAa,wBAAwB,aAAa,qBAAqB,QAAQ,cAAc,IAAI,GAAG;AACtG,YAAI,aAAa,OAAO,gBAAgB;AACtC,cAAI,iBAAiB,oBAAoB;AACvC,6BAAiB,iBAAiB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AAAA,UACpE,OAAO;AACL,6BAAiB,iBAAiB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AAAA,UACpE;AAAA,QACF,WAAW,iBAAiB,sBAAsB,aAAa,OAAO,mBAAmB,EAAG;AAC5F,qBAAa,QAAQ,gBAAgB,UAAU,IAAI,MAAS;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACA,KAAG,cAAc,MAAM;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,QAAI,CAAC,UAAU,CAAC,OAAO,OAAQ;AAC/B,QAAI,OAAO,OAAO,WAAW,YAAY,OAAO,kBAAkB,aAAa;AAC7E,YAAMC,YAAW,YAAY;AAC7B,YAAM,0BAA0B,MAAM;AACpC,cAAM,gBAAgB,OAAO,OAAO,WAAW,WAAWA,UAAS,cAAc,OAAO,MAAM,IAAI,OAAO;AACzG,YAAI,iBAAiB,cAAc,QAAQ;AACzC,iBAAO,SAAS,cAAc;AAC9B,eAAK;AACL,iBAAO,IAAI;AAAA,QACb,WAAW,eAAe;AACxB,gBAAM,YAAY,GAAG,OAAO,OAAO,YAAY;AAC/C,gBAAM,iBAAiB,OAAK;AAC1B,mBAAO,SAAS,EAAE,OAAO,CAAC;AAC1B,0BAAc,oBAAoB,WAAW,cAAc;AAC3D,iBAAK;AACL,mBAAO,IAAI;AACX,mBAAO,OAAO,OAAO;AACrB,mBAAO,OAAO;AAAA,UAChB;AACA,wBAAc,iBAAiB,WAAW,cAAc;AAAA,QAC1D;AACA,eAAO;AAAA,MACT;AACA,YAAM,yBAAyB,MAAM;AACnC,YAAI,OAAO,UAAW;AACtB,cAAM,gBAAgB,wBAAwB;AAC9C,YAAI,CAAC,eAAe;AAClB,gCAAsB,sBAAsB;AAAA,QAC9C;AAAA,MACF;AACA,4BAAsB,sBAAsB;AAAA,IAC9C,OAAO;AACL,WAAK;AACL,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,KAAG,4CAA4C,MAAM;AACnD,WAAO;AAAA,EACT,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa,UAAW;AAC7C,iBAAa,cAAc,QAAQ;AAAA,EACrC,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,UAAM,eAAe,OAAO,OAAO;AACnC,QAAI,CAAC,gBAAgB,aAAa,UAAW;AAC7C,QAAI,eAAe;AACjB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;AChMA,SAAS,SAAS,MAAM;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,UAAU;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,uBAAuB;AAAA,MACvB,QAAQ;AAAA,MACR,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACD,WAAS,eAAe;AACtB,QAAI,OAAO,OAAO,QAAS;AAC3B,UAAM,YAAY,OAAO,aAAa;AACtC,WAAO,aAAa,SAAS;AAC7B,WAAO,cAAc,CAAC;AACtB,WAAO,gBAAgB,WAAW,SAAS;AAC3C,WAAO,SAAS,WAAW;AAAA,MACzB,YAAY,OAAO,MAAM,OAAO,YAAY,CAAC,OAAO;AAAA,IACtD,CAAC;AAAA,EACH;AACA,WAAS,cAAc;AACrB,QAAI,OAAO,OAAO,QAAS;AAC3B,UAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB;AAAA,IACF,IAAI;AAEJ,QAAI,KAAK,WAAW,WAAW,GAAG;AAChC,WAAK,WAAW,KAAK;AAAA,QACnB,UAAU,QAAQ,OAAO,aAAa,IAAI,WAAW,QAAQ;AAAA,QAC7D,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AACA,SAAK,WAAW,KAAK;AAAA,MACnB,UAAU,QAAQ,OAAO,aAAa,IAAI,aAAa,UAAU;AAAA,MACjE,MAAM,IAAI;AAAA,IACZ,CAAC;AAAA,EACH;AACA,WAAS,WAAW,OAAO;AACzB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,OAAO,QAAS;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,IACnB,IAAI;AAEJ,UAAM,eAAe,IAAI;AACzB,UAAM,WAAW,eAAe,KAAK;AACrC,QAAI,aAAa,CAAC,OAAO,aAAa,GAAG;AACvC,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,QAAI,aAAa,CAAC,OAAO,aAAa,GAAG;AACvC,UAAI,OAAO,OAAO,SAAS,SAAS,QAAQ;AAC1C,eAAO,QAAQ,SAAS,SAAS,CAAC;AAAA,MACpC,OAAO;AACL,eAAO,QAAQ,OAAO,OAAO,SAAS,CAAC;AAAA,MACzC;AACA;AAAA,IACF;AACA,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,cAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,cAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,cAAM,WAAW,cAAc,WAAW,cAAc;AACxD,cAAM,OAAO,cAAc,OAAO,cAAc;AAChD,eAAO,WAAW,WAAW;AAC7B,eAAO,YAAY;AACnB,YAAI,KAAK,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,iBAAiB;AAC/D,iBAAO,WAAW;AAAA,QACpB;AAGA,YAAI,OAAO,OAAO,IAAI,IAAI,cAAc,OAAO,KAAK;AAClD,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF,OAAO;AACL,eAAO,WAAW;AAAA,MACpB;AACA,aAAO,YAAY,OAAO,SAAS;AACnC,WAAK,WAAW,SAAS;AACzB,UAAI,mBAAmB,MAAO,OAAO,SAAS;AAC9C,YAAM,mBAAmB,OAAO,WAAW;AAC3C,UAAI,cAAc,OAAO,YAAY;AACrC,UAAI,IAAK,eAAc,CAAC;AACxB,UAAI,WAAW;AACf,UAAI;AACJ,YAAM,eAAe,KAAK,IAAI,OAAO,QAAQ,IAAI,KAAK,OAAO,SAAS;AACtE,UAAI;AACJ,UAAI,cAAc,OAAO,aAAa,GAAG;AACvC,YAAI,OAAO,SAAS,gBAAgB;AAClC,cAAI,cAAc,OAAO,aAAa,IAAI,CAAC,cAAc;AACvD,0BAAc,OAAO,aAAa,IAAI;AAAA,UACxC;AACA,gCAAsB,OAAO,aAAa;AAC1C,qBAAW;AACX,eAAK,sBAAsB;AAAA,QAC7B,OAAO;AACL,wBAAc,OAAO,aAAa;AAAA,QACpC;AACA,YAAI,OAAO,QAAQ,OAAO,eAAgB,gBAAe;AAAA,MAC3D,WAAW,cAAc,OAAO,aAAa,GAAG;AAC9C,YAAI,OAAO,SAAS,gBAAgB;AAClC,cAAI,cAAc,OAAO,aAAa,IAAI,cAAc;AACtD,0BAAc,OAAO,aAAa,IAAI;AAAA,UACxC;AACA,gCAAsB,OAAO,aAAa;AAC1C,qBAAW;AACX,eAAK,sBAAsB;AAAA,QAC7B,OAAO;AACL,wBAAc,OAAO,aAAa;AAAA,QACpC;AACA,YAAI,OAAO,QAAQ,OAAO,eAAgB,gBAAe;AAAA,MAC3D,WAAW,OAAO,SAAS,QAAQ;AACjC,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,cAAI,SAAS,CAAC,IAAI,CAAC,aAAa;AAC9B,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,IAAI,SAAS,SAAS,IAAI,WAAW,IAAI,KAAK,IAAI,SAAS,YAAY,CAAC,IAAI,WAAW,KAAK,OAAO,mBAAmB,QAAQ;AACrI,wBAAc,SAAS,SAAS;AAAA,QAClC,OAAO;AACL,wBAAc,SAAS,YAAY,CAAC;AAAA,QACtC;AACA,sBAAc,CAAC;AAAA,MACjB;AACA,UAAI,cAAc;AAChB,aAAK,iBAAiB,MAAM;AAC1B,iBAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,UAAI,OAAO,aAAa,GAAG;AACzB,YAAI,KAAK;AACP,6BAAmB,KAAK,KAAK,CAAC,cAAc,OAAO,aAAa,OAAO,QAAQ;AAAA,QACjF,OAAO;AACL,6BAAmB,KAAK,KAAK,cAAc,OAAO,aAAa,OAAO,QAAQ;AAAA,QAChF;AACA,YAAI,OAAO,SAAS,QAAQ;AAQ1B,gBAAM,eAAe,KAAK,KAAK,MAAM,CAAC,cAAc,eAAe,OAAO,SAAS;AACnF,gBAAM,mBAAmB,OAAO,gBAAgB,OAAO,WAAW;AAClE,cAAI,eAAe,kBAAkB;AACnC,+BAAmB,OAAO;AAAA,UAC5B,WAAW,eAAe,IAAI,kBAAkB;AAC9C,+BAAmB,OAAO,QAAQ;AAAA,UACpC,OAAO;AACL,+BAAmB,OAAO,QAAQ;AAAA,UACpC;AAAA,QACF;AAAA,MACF,WAAW,OAAO,SAAS,QAAQ;AACjC,eAAO,eAAe;AACtB;AAAA,MACF;AACA,UAAI,OAAO,SAAS,kBAAkB,UAAU;AAC9C,eAAO,eAAe,mBAAmB;AACzC,eAAO,cAAc,gBAAgB;AACrC,eAAO,aAAa,WAAW;AAC/B,eAAO,gBAAgB,MAAM,OAAO,cAAc;AAClD,eAAO,YAAY;AACnB,6BAAqB,WAAW,MAAM;AACpC,cAAI,CAAC,UAAU,OAAO,aAAa,CAAC,KAAK,oBAAqB;AAC9D,eAAK,gBAAgB;AACrB,iBAAO,cAAc,OAAO,KAAK;AACjC,qBAAW,MAAM;AACf,mBAAO,aAAa,mBAAmB;AACvC,iCAAqB,WAAW,MAAM;AACpC,kBAAI,CAAC,UAAU,OAAO,UAAW;AACjC,qBAAO,cAAc;AAAA,YACvB,CAAC;AAAA,UACH,GAAG,CAAC;AAAA,QACN,CAAC;AAAA,MACH,WAAW,OAAO,UAAU;AAC1B,aAAK,4BAA4B;AACjC,eAAO,eAAe,WAAW;AACjC,eAAO,cAAc,gBAAgB;AACrC,eAAO,aAAa,WAAW;AAC/B,eAAO,gBAAgB,MAAM,OAAO,cAAc;AAClD,YAAI,CAAC,OAAO,WAAW;AACrB,iBAAO,YAAY;AACnB,+BAAqB,WAAW,MAAM;AACpC,gBAAI,CAAC,UAAU,OAAO,UAAW;AACjC,mBAAO,cAAc;AAAA,UACvB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,eAAO,eAAe,WAAW;AAAA,MACnC;AACA,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B,WAAW,OAAO,SAAS,QAAQ;AACjC,aAAO,eAAe;AACtB;AAAA,IACF,WAAW,OAAO,UAAU;AAC1B,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,CAAC,OAAO,SAAS,YAAY,YAAY,OAAO,cAAc;AAChE,WAAK,wBAAwB;AAC7B,aAAO,eAAe;AACtB,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC1OA,SAAS,KAAK,MAAM;AAClB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,kBAAkB,MAAM;AAC5B,QAAI,eAAe,OAAO,OAAO;AACjC,QAAI,OAAO,iBAAiB,YAAY,aAAa,QAAQ,GAAG,KAAK,GAAG;AACtE,qBAAe,WAAW,aAAa,QAAQ,KAAK,EAAE,CAAC,IAAI,MAAM,OAAO;AAAA,IAC1E,WAAW,OAAO,iBAAiB,UAAU;AAC3C,qBAAe,WAAW,YAAY;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,YAAU;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO;AAC7G,qBAAiB,KAAK,MAAM,eAAe,IAAI;AAC/C,QAAI,KAAK,MAAM,eAAe,IAAI,MAAM,eAAe,MAAM;AAC3D,+BAAyB;AAAA,IAC3B,OAAO;AACL,+BAAyB,KAAK,KAAK,eAAe,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,kBAAkB,UAAU,SAAS,OAAO;AAC9C,+BAAyB,KAAK,IAAI,wBAAwB,gBAAgB,IAAI;AAAA,IAChF;AACA,mBAAe,yBAAyB;AAAA,EAC1C;AACA,QAAM,cAAc,MAAM;AACxB,QAAI,OAAO,QAAQ;AACjB,aAAO,OAAO,QAAQ,WAAS;AAC7B,YAAI,MAAM,oBAAoB;AAC5B,gBAAM,MAAM,SAAS;AACrB,gBAAM,MAAM,OAAO,kBAAkB,YAAY,CAAC,IAAI;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,cAAc,CAAC,GAAG,OAAO,WAAW;AACxC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,UAAM,eAAe,gBAAgB;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,UAAM,eAAe,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO;AAE7G,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,SAAS,iBAAiB,GAAG;AACxC,YAAM,aAAa,KAAK,MAAM,KAAK,iBAAiB,KAAK;AACzD,YAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,YAAM,iBAAiB,eAAe,IAAI,iBAAiB,KAAK,IAAI,KAAK,MAAM,eAAe,aAAa,OAAO,kBAAkB,IAAI,GAAG,cAAc;AACzJ,YAAM,KAAK,MAAM,oBAAoB,cAAc;AACnD,eAAS,oBAAoB,MAAM,iBAAiB,aAAa;AACjE,2BAAqB,SAAS,MAAM,yBAAyB;AAC7D,YAAM,MAAM,QAAQ;AAAA,IACtB,WAAW,SAAS,UAAU;AAC5B,eAAS,KAAK,MAAM,IAAI,IAAI;AAC5B,YAAM,IAAI,SAAS;AACnB,UAAI,SAAS,kBAAkB,WAAW,kBAAkB,QAAQ,OAAO,GAAG;AAC5E,eAAO;AACP,YAAI,OAAO,MAAM;AACf,gBAAM;AACN,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,KAAK,MAAM,IAAI,YAAY;AACjC,eAAS,IAAI,MAAM;AAAA,IACrB;AACA,UAAM,MAAM;AACZ,UAAM,SAAS;AACf,UAAM,MAAM,SAAS,iBAAiB,OAAO,KAAK,YAAY,SAAS,IAAI;AAC3E,UAAM,MAAM,OAAO,kBAAkB,YAAY,CAAC,IAAI,QAAQ,IAAI,gBAAgB,GAAG,YAAY,OAAO;AACxG,UAAM,qBAAqB;AAAA,EAC7B;AACA,QAAM,oBAAoB,CAAC,WAAW,aAAa;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,eAAe,gBAAgB;AACrC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO,OAAO;AAClB,WAAO,eAAe,YAAY,gBAAgB;AAClD,WAAO,cAAc,KAAK,KAAK,OAAO,cAAc,IAAI,IAAI;AAC5D,QAAI,CAAC,OAAO,OAAO,SAAS;AAC1B,aAAO,UAAU,MAAM,OAAO,kBAAkB,OAAO,CAAC,IAAI,GAAG,OAAO,cAAc,YAAY;AAAA,IAClG;AACA,QAAI,gBAAgB;AAClB,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,YAAI,iBAAiB,SAAS,CAAC;AAC/B,YAAI,aAAc,kBAAiB,KAAK,MAAM,cAAc;AAC5D,YAAI,SAAS,CAAC,IAAI,OAAO,cAAc,SAAS,CAAC,EAAG,eAAc,KAAK,cAAc;AAAA,MACvF;AACA,eAAS,OAAO,GAAG,SAAS,MAAM;AAClC,eAAS,KAAK,GAAG,aAAa;AAAA,IAChC;AAAA,EACF;AACA,QAAM,SAAS,MAAM;AACnB,kBAAc,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,OAAO;AAAA,EAChE;AACA,QAAM,WAAW,MAAM;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,OAAO,QAAQ,OAAO,KAAK,OAAO;AACrD,QAAI,eAAe,CAAC,YAAY;AAC9B,SAAG,UAAU,OAAO,GAAG,OAAO,sBAAsB,QAAQ,GAAG,OAAO,sBAAsB,aAAa;AACzG,uBAAiB;AACjB,aAAO,qBAAqB;AAAA,IAC9B,WAAW,CAAC,eAAe,YAAY;AACrC,SAAG,UAAU,IAAI,GAAG,OAAO,sBAAsB,MAAM;AACvD,UAAI,OAAO,KAAK,SAAS,UAAU;AACjC,WAAG,UAAU,IAAI,GAAG,OAAO,sBAAsB,aAAa;AAAA,MAChE;AACA,aAAO,qBAAqB;AAAA,IAC9B;AACA,kBAAc;AAAA,EAChB;AACA,KAAG,QAAQ,MAAM;AACjB,KAAG,UAAU,QAAQ;AACrB,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACtJA,SAAS,YAAY,QAAQ;AAC3B,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,MAAM;AACf,WAAO,YAAY;AAAA,EACrB;AACA,QAAM,gBAAgB,aAAW;AAC/B,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,mBAAa,SAAS,OAAO;AAC7B,eAAS,OAAO,QAAQ,SAAS,CAAC,CAAC;AACnC,mBAAa,SAAS,EAAE;AAAA,IAC1B,OAAO;AACL,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC,EAAG,eAAc,OAAO,CAAC,CAAC;AAAA,IACxC;AAAA,EACF,OAAO;AACL,kBAAc,MAAM;AAAA,EACtB;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACF;AAEA,SAAS,aAAa,QAAQ;AAC5B,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,MAAM;AACf,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,iBAAiB,cAAc;AACnC,QAAM,iBAAiB,aAAW;AAChC,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,mBAAa,SAAS,OAAO;AAC7B,eAAS,QAAQ,QAAQ,SAAS,CAAC,CAAC;AACpC,mBAAa,SAAS,EAAE;AAAA,IAC1B,OAAO;AACL,eAAS,QAAQ,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC,EAAG,gBAAe,OAAO,CAAC,CAAC;AAAA,IACzC;AACA,qBAAiB,cAAc,OAAO;AAAA,EACxC,OAAO;AACL,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,QAAQ,gBAAgB,GAAG,KAAK;AACzC;AAEA,SAAS,SAAS,OAAO,QAAQ;AAC/B,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACxB,MAAI,OAAO,MAAM;AACf,yBAAqB,OAAO;AAC5B,WAAO,YAAY;AACnB,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,aAAa,OAAO,OAAO;AACjC,MAAI,SAAS,GAAG;AACd,WAAO,aAAa,MAAM;AAC1B;AAAA,EACF;AACA,MAAI,SAAS,YAAY;AACvB,WAAO,YAAY,MAAM;AACzB;AAAA,EACF;AACA,MAAI,iBAAiB,oBAAoB,QAAQ,oBAAoB,IAAI;AACzE,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,aAAa,GAAG,KAAK,OAAO,KAAK,GAAG;AAC/C,UAAM,eAAe,OAAO,OAAO,CAAC;AACpC,iBAAa,OAAO;AACpB,iBAAa,QAAQ,YAAY;AAAA,EACnC;AACA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACpD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAI,OAAO,CAAC,EAAG,UAAS,OAAO,OAAO,CAAC,CAAC;AAAA,IAC1C;AACA,qBAAiB,oBAAoB,QAAQ,oBAAoB,OAAO,SAAS;AAAA,EACnF,OAAO;AACL,aAAS,OAAO,MAAM;AAAA,EACxB;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC/C,aAAS,OAAO,aAAa,CAAC,CAAC;AAAA,EACjC;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,MAAM;AACf,WAAO,QAAQ,iBAAiB,OAAO,cAAc,GAAG,KAAK;AAAA,EAC/D,OAAO;AACL,WAAO,QAAQ,gBAAgB,GAAG,KAAK;AAAA,EACzC;AACF;AAEA,SAAS,YAAY,eAAe;AAClC,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACxB,MAAI,OAAO,MAAM;AACf,yBAAqB,OAAO;AAC5B,WAAO,YAAY;AAAA,EACrB;AACA,MAAI,iBAAiB;AACrB,MAAI;AACJ,MAAI,OAAO,kBAAkB,YAAY,YAAY,eAAe;AAClE,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,sBAAgB,cAAc,CAAC;AAC/B,UAAI,OAAO,OAAO,aAAa,EAAG,QAAO,OAAO,aAAa,EAAE,OAAO;AACtE,UAAI,gBAAgB,eAAgB,mBAAkB;AAAA,IACxD;AACA,qBAAiB,KAAK,IAAI,gBAAgB,CAAC;AAAA,EAC7C,OAAO;AACL,oBAAgB;AAChB,QAAI,OAAO,OAAO,aAAa,EAAG,QAAO,OAAO,aAAa,EAAE,OAAO;AACtE,QAAI,gBAAgB,eAAgB,mBAAkB;AACtD,qBAAiB,KAAK,IAAI,gBAAgB,CAAC;AAAA,EAC7C;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,MAAM;AACf,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,CAAC,OAAO,YAAY,OAAO,WAAW;AACxC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,MAAM;AACf,WAAO,QAAQ,iBAAiB,OAAO,cAAc,GAAG,KAAK;AAAA,EAC/D,OAAO;AACL,WAAO,QAAQ,gBAAgB,GAAG,KAAK;AAAA,EACzC;AACF;AAEA,SAAS,kBAAkB;AACzB,QAAM,SAAS;AACf,QAAM,gBAAgB,CAAC;AACvB,WAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK,GAAG;AAChD,kBAAc,KAAK,CAAC;AAAA,EACtB;AACA,SAAO,YAAY,aAAa;AAClC;AAEA,SAAS,aAAa,MAAM;AAC1B,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,SAAO,OAAO,QAAQ;AAAA,IACpB,aAAa,YAAY,KAAK,MAAM;AAAA,IACpC,cAAc,aAAa,KAAK,MAAM;AAAA,IACtC,UAAU,SAAS,KAAK,MAAM;AAAA,IAC9B,aAAa,YAAY,KAAK,MAAM;AAAA,IACpC,iBAAiB,gBAAgB,KAAK,MAAM;AAAA,EAC9C,CAAC;AACH;;;AC9LA,SAAS,WAAW,QAAQ;AAC1B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,KAAG,cAAc,MAAM;AACrB,QAAI,OAAO,OAAO,WAAW,OAAQ;AACrC,WAAO,WAAW,KAAK,GAAG,OAAO,OAAO,sBAAsB,GAAG,MAAM,EAAE;AACzE,QAAI,eAAe,YAAY,GAAG;AAChC,aAAO,WAAW,KAAK,GAAG,OAAO,OAAO,sBAAsB,IAAI;AAAA,IACpE;AACA,UAAM,wBAAwB,kBAAkB,gBAAgB,IAAI,CAAC;AACrE,WAAO,OAAO,OAAO,QAAQ,qBAAqB;AAClD,WAAO,OAAO,OAAO,gBAAgB,qBAAqB;AAAA,EAC5D,CAAC;AACD,KAAG,gCAAgC,MAAM;AACvC,QAAI,OAAO,OAAO,WAAW,OAAQ;AACrC,iBAAa;AAAA,EACf,CAAC;AACD,KAAG,iBAAiB,CAAC,IAAI,aAAa;AACpC,QAAI,OAAO,OAAO,WAAW,OAAQ;AACrC,kBAAc,QAAQ;AAAA,EACxB,CAAC;AACD,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,OAAO,WAAW,OAAQ;AACrC,QAAI,iBAAiB;AACnB,UAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,aAAc;AAEzD,aAAO,OAAO,QAAQ,aAAW;AAC/B,gBAAQ,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY,SAAS,OAAO,CAAC;AAAA,MAChL,CAAC;AAED,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,MAAI;AACJ,KAAG,iBAAiB,MAAM;AACxB,QAAI,OAAO,OAAO,WAAW,OAAQ;AACrC,QAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,+BAAyB;AAAA,IAC3B;AACA,0BAAsB,MAAM;AAC1B,UAAI,0BAA0B,OAAO,UAAU,OAAO,OAAO,QAAQ;AACnE,qBAAa;AACb,iCAAyB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;ACrDA,SAAS,aAAa,cAAc,SAAS;AAC3C,QAAM,cAAc,oBAAoB,OAAO;AAC/C,MAAI,gBAAgB,SAAS;AAC3B,gBAAY,MAAM,qBAAqB;AACvC,gBAAY,MAAM,6BAA6B,IAAI;AAAA,EACrD;AACA,SAAO;AACT;;;ACPA,SAAS,2BAA2B,MAAM;AACxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,QAAM;AACrB,QAAI,CAAC,GAAG,eAAe;AAErB,YAAM,QAAQ,OAAO,OAAO,KAAK,aAAW,QAAQ,cAAc,QAAQ,eAAe,GAAG,UAAU;AACtG,aAAO;AAAA,IACT;AACA,WAAO,GAAG;AAAA,EACZ;AACA,MAAI,OAAO,OAAO,oBAAoB,aAAa,GAAG;AACpD,QAAI,iBAAiB;AACrB,QAAI;AACJ,QAAI,WAAW;AACb,4BAAsB;AAAA,IACxB,OAAO;AACL,4BAAsB,kBAAkB,OAAO,iBAAe;AAC5D,cAAM,KAAK,YAAY,UAAU,SAAS,wBAAwB,IAAI,SAAS,WAAW,IAAI;AAC9F,eAAO,OAAO,cAAc,EAAE,MAAM;AAAA,MACtC,CAAC;AAAA,IACH;AACA,wBAAoB,QAAQ,QAAM;AAChC,2BAAqB,IAAI,MAAM;AAC7B,YAAI,eAAgB;AACpB,YAAI,CAAC,UAAU,OAAO,UAAW;AACjC,yBAAiB;AACjB,eAAO,YAAY;AACnB,cAAM,MAAM,IAAI,OAAO,YAAY,iBAAiB;AAAA,UAClD,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AACD,eAAO,UAAU,cAAc,GAAG;AAAA,MACpC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;ACxCA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,OAAO,CAAC;AAC/B,YAAM,SAAS,QAAQ;AACvB,UAAI,KAAK,CAAC;AACV,UAAI,CAAC,OAAO,OAAO,iBAAkB,OAAM,OAAO;AAClD,UAAI,KAAK;AACT,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,eAAe,OAAO,OAAO,WAAW,YAAY,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,EAAE,GAAG,CAAC;AACtJ,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,UAAU;AACzB,eAAS,MAAM,YAAY,eAAe,EAAE,OAAO,EAAE;AAAA,IACvD;AAAA,EACF;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG,QAAQ;AAAA,IAC3C,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;AC5DA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACD,QAAM,qBAAqB,CAAC,SAAS,UAAU,iBAAiB;AAC9D,QAAI,eAAe,eAAe,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AACvI,QAAI,cAAc,eAAe,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AAC1I,QAAI,CAAC,cAAc;AACjB,qBAAe,cAAc,OAAO,gDAAgD,eAAe,SAAS,KAAK,GAAG,MAAM,GAAG,CAAC;AAC9H,cAAQ,OAAO,YAAY;AAAA,IAC7B;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,cAAc,OAAO,gDAAgD,eAAe,UAAU,QAAQ,GAAG,MAAM,GAAG,CAAC;AACjI,cAAQ,OAAO,WAAW;AAAA,IAC5B;AACA,QAAI,aAAc,cAAa,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;AACpE,QAAI,YAAa,aAAY,MAAM,UAAU,KAAK,IAAI,UAAU,CAAC;AAAA,EACnE;AACA,QAAM,kBAAkB,MAAM;AAE5B,UAAM,eAAe,OAAO,aAAa;AACzC,WAAO,OAAO,QAAQ,aAAW;AAC/B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAC3D,yBAAmB,SAAS,UAAU,YAAY;AAAA,IACpD,CAAC;AAAA,EACH;AACA,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,MAAM;AAAA,MACN;AAAA,IACF,IAAI;AACJ,UAAM,IAAI,aAAa,MAAM;AAC7B,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,eAAe,OAAO,aAAa;AACzC,UAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,QAAI,gBAAgB;AACpB,QAAI;AACJ,QAAI,OAAO,QAAQ;AACjB,UAAI,cAAc;AAChB,uBAAe,OAAO,UAAU,cAAc,qBAAqB;AACnE,YAAI,CAAC,cAAc;AACjB,yBAAe,cAAc,OAAO,oBAAoB;AACxD,iBAAO,UAAU,OAAO,YAAY;AAAA,QACtC;AACA,qBAAa,MAAM,SAAS,GAAG,WAAW;AAAA,MAC5C,OAAO;AACL,uBAAe,GAAG,cAAc,qBAAqB;AACrD,YAAI,CAAC,cAAc;AACjB,yBAAe,cAAc,OAAO,oBAAoB;AACxD,aAAG,OAAO,YAAY;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,UAAI,aAAa;AACjB,UAAI,WAAW;AACb,qBAAa,SAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE;AAAA,MAC3E;AACA,UAAI,aAAa,aAAa;AAC9B,UAAI,QAAQ,KAAK,MAAM,aAAa,GAAG;AACvC,UAAI,KAAK;AACP,qBAAa,CAAC;AACd,gBAAQ,KAAK,MAAM,CAAC,aAAa,GAAG;AAAA,MACtC;AACA,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAC3D,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,aAAa,MAAM,GAAG;AACxB,aAAK,CAAC,QAAQ,IAAI;AAClB,aAAK;AAAA,MACP,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK;AACL,aAAK,CAAC,QAAQ,IAAI;AAAA,MACpB,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK,aAAa,QAAQ,IAAI;AAC9B,aAAK;AAAA,MACP,YAAY,aAAa,KAAK,MAAM,GAAG;AACrC,aAAK,CAAC;AACN,aAAK,IAAI,aAAa,aAAa,IAAI;AAAA,MACzC;AACA,UAAI,KAAK;AACP,aAAK,CAAC;AAAA,MACR;AACA,UAAI,CAAC,cAAc;AACjB,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,YAAY,WAAW,EAAE,eAAe,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,eAAe,aAAa,CAAC,CAAC,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE;AACtJ,UAAI,YAAY,KAAK,WAAW,IAAI;AAClC,wBAAgB,aAAa,KAAK,WAAW;AAC7C,YAAI,IAAK,iBAAgB,CAAC,aAAa,KAAK,WAAW;AAAA,MACzD;AACA,cAAQ,MAAM,YAAY;AAC1B,UAAI,OAAO,cAAc;AACvB,2BAAmB,SAAS,UAAU,YAAY;AAAA,MACpD;AAAA,IACF;AACA,cAAU,MAAM,kBAAkB,YAAY,aAAa,CAAC;AAC5D,cAAU,MAAM,0BAA0B,IAAI,YAAY,aAAa,CAAC;AACxE,QAAI,OAAO,QAAQ;AACjB,UAAI,cAAc;AAChB,qBAAa,MAAM,YAAY,oBAAoB,cAAc,IAAI,OAAO,YAAY,OAAO,CAAC,cAAc,CAAC,6CAA6C,OAAO,WAAW;AAAA,MAChL,OAAO;AACL,cAAM,cAAc,KAAK,IAAI,aAAa,IAAI,KAAK,MAAM,KAAK,IAAI,aAAa,IAAI,EAAE,IAAI;AACzF,cAAM,aAAa,OAAO,KAAK,IAAI,cAAc,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,KAAK,GAAG,IAAI;AACtH,cAAM,SAAS,OAAO;AACtB,cAAM,SAAS,OAAO,cAAc;AACpC,cAAM,SAAS,OAAO;AACtB,qBAAa,MAAM,YAAY,WAAW,MAAM,QAAQ,MAAM,sBAAsB,eAAe,IAAI,MAAM,OAAO,CAAC,eAAe,IAAI,MAAM;AAAA,MAChJ;AAAA,IACF;AACA,UAAM,WAAW,QAAQ,YAAY,QAAQ,cAAc,QAAQ,qBAAqB,CAAC,aAAa,IAAI;AAC1G,cAAU,MAAM,YAAY,qBAAqB,OAAO,eAAe,EAAE,OAAO,aAAa,IAAI,IAAI,aAAa,CAAC,gBAAgB,EAAE,OAAO,aAAa,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAChL,cAAU,MAAM,YAAY,6BAA6B,GAAG,OAAO,IAAI;AAAA,EACzE;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,aAAW;AACxB,cAAQ,MAAM,qBAAqB,GAAG,QAAQ;AAC9C,cAAQ,iBAAiB,8GAA8G,EAAE,QAAQ,WAAS;AACxJ,cAAM,MAAM,qBAAqB,GAAG,QAAQ;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,OAAO,WAAW,UAAU,CAAC,OAAO,aAAa,GAAG;AAC7D,YAAM,WAAW,GAAG,cAAc,qBAAqB;AACvD,UAAI,SAAU,UAAS,MAAM,qBAAqB,GAAG,QAAQ;AAAA,IAC/D;AAAA,EACF;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,MAAM,OAAO,OAAO;AAAA,IACrC,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACH;;;ACzKA,SAAS,aAAa,QAAQ,SAAS,MAAM;AAC3C,QAAM,cAAc,sBAAsB,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG,SAAS,wBAAwB,MAAM,KAAK,EAAE;AACjH,QAAM,kBAAkB,oBAAoB,OAAO;AACnD,MAAI,WAAW,gBAAgB,cAAc,IAAI,YAAY,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE;AACnF,MAAI,CAAC,UAAU;AACb,eAAW,cAAc,OAAO,YAAY,MAAM,GAAG,CAAC;AACtD,oBAAgB,OAAO,QAAQ;AAAA,EACjC;AACA,SAAO;AACT;;;ACLA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,YAAY;AAAA,MACV,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AAAA,EACF,CAAC;AACD,QAAM,qBAAqB,CAAC,SAAS,aAAa;AAChD,QAAI,eAAe,OAAO,aAAa,IAAI,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AAChJ,QAAI,cAAc,OAAO,aAAa,IAAI,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AACnJ,QAAI,CAAC,cAAc;AACjB,qBAAe,aAAa,QAAQ,SAAS,OAAO,aAAa,IAAI,SAAS,KAAK;AAAA,IACrF;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,aAAa,QAAQ,SAAS,OAAO,aAAa,IAAI,UAAU,QAAQ;AAAA,IACxF;AACA,QAAI,aAAc,cAAa,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;AACpE,QAAI,YAAa,aAAY,MAAM,UAAU,KAAK,IAAI,UAAU,CAAC;AAAA,EACnE;AACA,QAAM,kBAAkB,MAAM;AAE5B,WAAO,OAAO;AACd,WAAO,OAAO,QAAQ,aAAW;AAC/B,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,OAAO,WAAW,eAAe;AAC1C,mBAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAAA,MACvD;AACA,yBAAmB,SAAS,QAAQ;AAAA,IACtC,CAAC;AAAA,EACH;AACA,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,YAAY,aAAa,MAAM;AACrC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,OAAO,WAAW,eAAe;AAC1C,mBAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,EAAE;AAAA,MACvD;AACA,YAAM,SAAS,QAAQ;AACvB,YAAM,SAAS,OAAO;AACtB,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,KAAK,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC;AAC/D,UAAI,KAAK;AACT,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,aAAK;AACL,aAAK;AACL,kBAAU,CAAC;AACX,kBAAU;AAAA,MACZ,WAAW,KAAK;AACd,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,QAAQ,CAAC,IAAI,OAAO;AAChE,UAAI,OAAO,cAAc;AACvB,2BAAmB,SAAS,QAAQ;AAAA,MACtC;AACA,YAAM,YAAY,eAAe,EAAE,OAAO,EAAE,oBAAoB,UAAU,OAAO,CAAC,gBAAgB,UAAU,OAAO,CAAC;AACpH,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG,QAAQ;AACzC,SAAG,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY;AACtJ,iBAAS,MAAM,qBAAqB,GAAG,QAAQ;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,MAAM,OAAO,OAAO;AAAA,IACrC,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;ACvGA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,iBAAiB;AAAA,MACf,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM,eAAe,OAAO,aAAa;AACzC,UAAM,YAAY,OAAO;AACzB,UAAM,SAAS,eAAe,CAAC,YAAY,cAAc,IAAI,CAAC,YAAY,eAAe;AACzF,UAAM,SAAS,eAAe,OAAO,SAAS,CAAC,OAAO;AACtD,UAAM,YAAY,OAAO;AACzB,UAAM,IAAI,aAAa,MAAM;AAE7B,aAAS,IAAI,GAAG,SAAS,OAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG;AAC1D,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,YAAY,gBAAgB,CAAC;AACnC,YAAM,cAAc,QAAQ;AAC5B,YAAM,gBAAgB,SAAS,cAAc,YAAY,KAAK;AAC9D,YAAM,mBAAmB,OAAO,OAAO,aAAa,aAAa,OAAO,SAAS,YAAY,IAAI,eAAe,OAAO;AACvH,UAAI,UAAU,eAAe,SAAS,mBAAmB;AACzD,UAAI,UAAU,eAAe,IAAI,SAAS;AAE1C,UAAI,aAAa,CAAC,YAAY,KAAK,IAAI,gBAAgB;AACvD,UAAI,UAAU,OAAO;AAErB,UAAI,OAAO,YAAY,YAAY,QAAQ,QAAQ,GAAG,MAAM,IAAI;AAC9D,kBAAU,WAAW,OAAO,OAAO,IAAI,MAAM;AAAA,MAC/C;AACA,UAAI,aAAa,eAAe,IAAI,UAAU;AAC9C,UAAI,aAAa,eAAe,UAAU,mBAAmB;AAC7D,UAAI,QAAQ,KAAK,IAAI,OAAO,SAAS,KAAK,IAAI,gBAAgB;AAG9D,UAAI,KAAK,IAAI,UAAU,IAAI,KAAO,cAAa;AAC/C,UAAI,KAAK,IAAI,UAAU,IAAI,KAAO,cAAa;AAC/C,UAAI,KAAK,IAAI,UAAU,IAAI,KAAO,cAAa;AAC/C,UAAI,KAAK,IAAI,OAAO,IAAI,KAAO,WAAU;AACzC,UAAI,KAAK,IAAI,OAAO,IAAI,KAAO,WAAU;AACzC,UAAI,KAAK,IAAI,KAAK,IAAI,KAAO,SAAQ;AACrC,YAAM,iBAAiB,eAAe,UAAU,MAAM,UAAU,MAAM,UAAU,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC,cAAc,KAAK;AACvJ,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAC3B,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,gBAAgB,CAAC,IAAI;AACjE,UAAI,OAAO,cAAc;AAEvB,YAAI,iBAAiB,eAAe,QAAQ,cAAc,2BAA2B,IAAI,QAAQ,cAAc,0BAA0B;AACzI,YAAI,gBAAgB,eAAe,QAAQ,cAAc,4BAA4B,IAAI,QAAQ,cAAc,6BAA6B;AAC5I,YAAI,CAAC,gBAAgB;AACnB,2BAAiB,aAAa,aAAa,SAAS,eAAe,SAAS,KAAK;AAAA,QACnF;AACA,YAAI,CAAC,eAAe;AAClB,0BAAgB,aAAa,aAAa,SAAS,eAAe,UAAU,QAAQ;AAAA,QACtF;AACA,YAAI,eAAgB,gBAAe,MAAM,UAAU,mBAAmB,IAAI,mBAAmB;AAC7F,YAAI,cAAe,eAAc,MAAM,UAAU,CAAC,mBAAmB,IAAI,CAAC,mBAAmB;AAAA,MAC/F;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG,QAAQ;AACzC,SAAG,iBAAiB,8GAA8G,EAAE,QAAQ,cAAY;AACtJ,iBAAS,MAAM,qBAAqB,GAAG,QAAQ;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,qBAAqB;AAAA,IACvB;AAAA,EACF,CAAC;AACH;;;AC/FA,SAAS,eAAe,MAAM;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,gBAAgB;AAAA,MACd,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,MAAM;AAAA,QACJ,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,QACJ,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,oBAAoB,WAAS;AACjC,QAAI,OAAO,UAAU,SAAU,QAAO;AACtC,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ,oBAAoB;AAAA,IACtB,IAAI;AACJ,UAAM,mBAAmB,OAAO,OAAO;AACvC,UAAM,YAAY,aAAa,MAAM;AACrC,QAAI,kBAAkB;AACpB,YAAM,SAAS,gBAAgB,CAAC,IAAI,IAAI,OAAO,OAAO,sBAAsB;AAC5E,gBAAU,MAAM,YAAY,yBAAyB,MAAM;AAAA,IAC7D;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,CAAC,OAAO,aAAa,GAAG,OAAO,aAAa;AACjG,UAAI,mBAAmB;AACvB,UAAI,CAAC,kBAAkB;AACrB,2BAAmB,KAAK,IAAI,KAAK,IAAI,QAAQ,kBAAkB,CAAC,OAAO,aAAa,GAAG,OAAO,aAAa;AAAA,MAC7G;AACA,YAAM,SAAS,QAAQ;AACvB,YAAM,IAAI,CAAC,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC,QAAQ,GAAG,CAAC;AAC7E,YAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AAClB,UAAI,SAAS;AACb,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,UAAE,CAAC,IAAI,EAAE,CAAC;AACV,UAAE,CAAC,IAAI;AAAA,MACT;AACA,UAAI,OAAO;AAAA,QACT,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,QACnB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AAChB,eAAO,OAAO;AACd,iBAAS;AAAA,MACX,WAAW,WAAW,GAAG;AACvB,eAAO,OAAO;AACd,iBAAS;AAAA,MACX;AAEA,QAAE,QAAQ,CAAC,OAAO,UAAU;AAC1B,UAAE,KAAK,IAAI,QAAQ,KAAK,SAAS,kBAAkB,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,KAAK,IAAI,WAAW,UAAU,CAAC;AAAA,MAChH,CAAC;AAED,QAAE,QAAQ,CAAC,OAAO,UAAU;AAC1B,YAAI,MAAM,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,WAAW,UAAU;AAC7D,UAAE,KAAK,IAAI;AAAA,MACb,CAAC;AACD,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,aAAa,CAAC,IAAI,OAAO;AACrE,YAAM,kBAAkB,EAAE,KAAK,IAAI;AACnC,YAAM,eAAe,WAAW,UAAU,EAAE,CAAC,CAAC,CAAC,gBAAgB,UAAU,EAAE,CAAC,CAAC,CAAC,gBAAgB,UAAU,EAAE,CAAC,CAAC,CAAC;AAC7G,YAAM,cAAc,mBAAmB,IAAI,SAAS,KAAK,IAAI,KAAK,SAAS,mBAAmB,UAAU,MAAM,SAAS,KAAK,IAAI,KAAK,SAAS,mBAAmB,UAAU;AAC3K,YAAM,gBAAgB,mBAAmB,IAAI,KAAK,IAAI,KAAK,WAAW,mBAAmB,aAAa,KAAK,IAAI,KAAK,WAAW,mBAAmB;AAClJ,YAAM,YAAY,eAAe,eAAe,KAAK,YAAY,IAAI,WAAW;AAGhF,UAAI,UAAU,KAAK,UAAU,CAAC,QAAQ;AACpC,YAAI,WAAW,QAAQ,cAAc,sBAAsB;AAC3D,YAAI,CAAC,YAAY,KAAK,QAAQ;AAC5B,qBAAW,aAAa,YAAY,OAAO;AAAA,QAC7C;AACA,YAAI,UAAU;AACZ,gBAAM,gBAAgB,OAAO,oBAAoB,YAAY,IAAI,OAAO,iBAAiB;AACzF,mBAAS,MAAM,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC;AAAA,QAC3E;AAAA,MACF;AACA,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAC3B,eAAS,MAAM,UAAU;AACzB,UAAI,KAAK,QAAQ;AACf,iBAAS,MAAM,kBAAkB,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG,QAAQ;AACzC,SAAG,iBAAiB,sBAAsB,EAAE,QAAQ,cAAY;AAC9D,iBAAS,MAAM,qBAAqB,GAAG,QAAQ;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,MAAM,OAAO,OAAO,eAAe;AAAA,IAChD,iBAAiB,OAAO;AAAA,MACtB,qBAAqB;AAAA,MACrB,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;;;ACzIA,SAAS,YAAY,MAAM;AACzB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,eAAa;AAAA,IACX,aAAa;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAM,eAAe,MAAM;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,IAChB,IAAI;AACJ,UAAM,SAAS,OAAO,OAAO;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,mBAAmB,MAAM,CAAC,OAAO,YAAY,OAAO;AAC1D,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,OAAO,CAAC;AACxB,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE,GAAG,CAAC;AACxD,UAAI,SAAS,QAAQ;AACrB,UAAI,OAAO,OAAO,kBAAkB,CAAC,OAAO,OAAO,SAAS;AAC1D,eAAO,UAAU,MAAM,YAAY,cAAc,OAAO,aAAa,CAAC;AAAA,MACxE;AACA,UAAI,OAAO,OAAO,kBAAkB,OAAO,OAAO,SAAS;AACzD,kBAAU,OAAO,CAAC,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,OAAO,OAAO,UAAU,CAAC,SAAS,OAAO,YAAY,CAAC;AAC/D,UAAI,KAAK;AACT,YAAM,KAAK,OAAO,KAAK,IAAI,QAAQ;AACnC,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC,OAAO,iBAAiB;AACtC,UAAI,QAAQ,OAAO,iBAAiB,KAAK,IAAI,QAAQ,IAAI;AACzD,YAAM,aAAa,OAAO,WAAW,OAAO,OAAO,QAAQ,UAAU,OAAO,QAAQ,OAAO,IAAI;AAC/F,YAAM,iBAAiB,eAAe,eAAe,eAAe,cAAc,MAAM,WAAW,KAAK,WAAW,MAAM,aAAa,OAAO,OAAO,YAAY,mBAAmB;AACnL,YAAM,iBAAiB,eAAe,eAAe,eAAe,cAAc,MAAM,WAAW,KAAK,WAAW,OAAO,aAAa,OAAO,OAAO,YAAY,mBAAmB;AACpL,UAAI,iBAAiB,eAAe;AAClC,cAAM,eAAe,IAAI,KAAK,KAAK,KAAK,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM;AACxE,kBAAU,MAAM,WAAW;AAC3B,iBAAS,OAAO;AAChB,iBAAS,KAAK;AACd,aAAK,GAAG,MAAM,cAAc,KAAK,IAAI,QAAQ,CAAC;AAAA,MAChD;AACA,UAAI,WAAW,GAAG;AAEhB,aAAK,QAAQ,EAAE,MAAM,MAAM,MAAM,GAAG,KAAK,QAAQ,KAAK,IAAI,QAAQ,CAAC;AAAA,MACrE,WAAW,WAAW,GAAG;AAEvB,aAAK,QAAQ,EAAE,MAAM,MAAM,MAAM,GAAG,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC;AAAA,MACtE,OAAO;AACL,aAAK,GAAG,EAAE;AAAA,MACZ;AACA,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B,cAAM,QAAQ;AACd,aAAK;AACL,aAAK;AAAA,MACP;AACA,YAAM,cAAc,WAAW,IAAI,GAAG,KAAK,IAAI,SAAS,QAAQ,KAAK,GAAG,KAAK,IAAI,SAAS,QAAQ;AAGlG,YAAM,YAAY;AAAA,sBACF,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,kBACpB,OAAO,SAAS,MAAM,CAAC,SAAS,SAAS,CAAC;AAAA,gBAC5C,WAAW;AAAA;AAIrB,UAAI,OAAO,cAAc;AAEvB,YAAI,WAAW,QAAQ,cAAc,sBAAsB;AAC3D,YAAI,CAAC,UAAU;AACb,qBAAW,aAAa,SAAS,OAAO;AAAA,QAC1C;AACA,YAAI,SAAU,UAAS,MAAM,UAAU,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,QAAQ,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC;AAAA,MAClG;AACA,cAAQ,MAAM,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,aAAa,CAAC,IAAI,OAAO;AACrE,YAAM,WAAW,aAAa,QAAQ,OAAO;AAC7C,eAAS,MAAM,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,gBAAgB,cAAY;AAChC,UAAM,oBAAoB,OAAO,OAAO,IAAI,aAAW,oBAAoB,OAAO,CAAC;AACnF,sBAAkB,QAAQ,QAAM;AAC9B,SAAG,MAAM,qBAAqB,GAAG,QAAQ;AACzC,SAAG,iBAAiB,sBAAsB,EAAE,QAAQ,cAAY;AAC9D,iBAAS,MAAM,qBAAqB,GAAG,QAAQ;AAAA,MACjD,CAAC;AAAA,IACH,CAAC;AACD,+BAA2B;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAW;AAAA,IACT,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,iBAAiB,OAAO;AAAA,MACtB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,sBAAsB,OAAO,OAAO,YAAY,SAAS,IAAI;AAAA,MAC7D,gBAAgB;AAAA,MAChB,kBAAkB,CAAC,OAAO,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;", "names": ["document", "appendSlide", "prependSlide", "removeSlide", "removeAllSlides", "document", "window", "event", "window", "event", "document", "window", "event", "document", "window", "document", "window", "document", "document"]}