import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMoneyBillWave,
  FaShoppingCart,
  FaEye,
  FaArrowLeft,
  FaSync,
  FaUsers,
  FaFilter,
  FaChevronLeft,
  FaChevronRight,
  FaMapMarkerAlt,
  FaToggleOn
} from 'react-icons/fa';
import { useAuthStore } from '../stores/authStore';
import api from '../lib/axios';
import Modal from '../components/Modal';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import SuccessModal from '../components/SuccessModal';
import { TextInput, TextArea } from '../components/inputs';
import ToggleSwitch from '../components/ToggleSwitch';
import SelectBox from '../components/SelectBox';

interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  is_active: boolean;
  total_debt: number;
  total_sales: number;
  created_at: string;
  updated_at?: string;
}

interface CustomerFormData {
  name: string;
  phone: string;
  email: string;
  address: string;
  notes: string;
  is_active: boolean;
}

const Customers: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, isInitialized } = useAuthStore();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [showInactiveOnly, setShowInactiveOnly] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1
  });
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<Partial<CustomerFormData>>({});
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    totalDebts: 0,
    totalPaidSales: 0
  });


  useEffect(() => {
    console.log('Auth state:', { user, isAuthenticated, isInitialized });

    // Wait for auth to be initialized
    if (!isInitialized) {
      console.log('Auth not initialized yet, waiting...');
      return;
    }

    // Check if user is logged in
    if (!isAuthenticated || !user) {
      console.log('User not authenticated, redirecting to login');
      navigate('/login');
      return;
    }

    console.log('User authenticated:', user);
    fetchCustomers();
    fetchStats();
  }, [user, isAuthenticated, isInitialized, navigate]); // Removed showActiveOnly dependency

  const fetchStats = async () => {
    try {
      console.log('Fetching customer stats...');
      const response = await api.get('/api/customers/stats');
      console.log('Customer stats response:', response.data);
      setStats(response.data);
      console.log('Stats state updated:', response.data);
    } catch (error) {
      console.error('Error fetching customer stats:', error);
      // Set default stats on error
      setStats({
        totalCustomers: 0,
        activeCustomers: 0,
        totalDebts: 0,
        totalPaidSales: 0
      });
    }
  };

  const fetchCustomers = async (page = currentPage, limit = itemsPerPage, customActiveOnly: boolean | null = null, customInactiveOnly: boolean | null = null, customSearch: string | null = null) => {
    try {
      setLoading(true);
      setConnectionError(null); // Clear previous errors

      console.log('Fetching customers...');

      // Use custom values if provided, otherwise use state values
      const activeOnly = customActiveOnly !== null ? customActiveOnly : showActiveOnly;
      const inactiveOnly = customInactiveOnly !== null ? customInactiveOnly : showInactiveOnly;
      const searchQuery = customSearch !== null ? customSearch : searchTerm;

      // Determine active filter based on toggle states
      let activeFilter = undefined;
      if (activeOnly && !inactiveOnly) {
        activeFilter = true;  // Show only active customers
      } else if (inactiveOnly && !activeOnly) {
        activeFilter = false; // Show only inactive customers
      }
      // If both are false (default) or both are true, show all customers

      const response = await api.get('/api/customers', {
        params: {
          active_only: activeFilter,
          search: searchQuery,
          skip: (page - 1) * limit,  // Convert page to skip
          limit: limit
        }
      });

      console.log('Customers fetched successfully:', response.data.length, 'customers');

      // Handle pagination - if backend doesn't support it, do client-side pagination
      let allCustomers = response.data;
      let paginationInfo;

      if (response.headers['x-total-count']) {
        // Backend supports pagination
        const total = parseInt(response.headers['x-total-count']);
        paginationInfo = {
          total: total,
          page: page,
          limit: limit,
          pages: Math.ceil(total / limit)
        };
        setCustomers(allCustomers);
      } else {
        // Client-side pagination
        const total = allCustomers.length;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedCustomers = allCustomers.slice(startIndex, endIndex);

        paginationInfo = {
          total: total,
          page: page,
          limit: limit,
          pages: Math.ceil(total / limit)
        };
        setCustomers(paginatedCustomers);
      }

      setPagination(paginationInfo);
      setCurrentPage(page);
      setConnectionError(null); // Clear error on success
    } catch (error: any) {
      console.error('Error fetching customers:', error);

      if (error.response?.status === 401) {
        console.log('401 error during fetch - auth will be handled by axios interceptor');
        setConnectionError('انتهت صلاحية الجلسة. يتم إعادة التوجيه لتسجيل الدخول...');
        // The axios interceptor will handle token refresh or redirect to login
      } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        setConnectionError('لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم.');
      } else {
        setConnectionError('حدث خطأ غير متوقع');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchCustomers();
  };

  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      notes: '',
      is_active: true
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.address || '',
      notes: customer.notes || '',
      is_active: customer.is_active
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleDeleteCustomer = (customer: Customer) => {
    setCustomerToDelete(customer);
    setShowDeleteModal(true);
  };

  const validateForm = (): boolean => {
    const errors: Partial<CustomerFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'اسم العميل مطلوب';
    }

    if (formData.email && !formData.email.includes('@')) {
      errors.email = 'البريد الإلكتروني غير صحيح';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      console.log('Starting customer save process...');
      console.log('Customer data:', formData);

      if (editingCustomer) {
        console.log('Updating existing customer:', editingCustomer.id);
        const response = await api.put(`/api/customers/${editingCustomer.id}`, formData);
        console.log('Customer updated successfully:', response.data);
      } else {
        console.log('Creating new customer');
        const response = await api.post('/api/customers', formData);
        console.log('Customer created successfully:', response.data);
      }

      setShowModal(false);
      setSuccessMessage(editingCustomer ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح');
      setShowSuccessModal(true);
      fetchCustomers();
    } catch (error: any) {
      console.error('Error saving customer:', error);

      if (error.response?.status === 401) {
        console.log('401 error during save - auth will be handled by axios interceptor');
        // The axios interceptor will handle token refresh or redirect to login
      } else if (error.response?.status === 400) {
        console.error('Validation error:', error.response.data);
        // Handle validation errors here if needed
      } else {
        console.error('Unexpected error:', error.message);
      }
    }
  };

  const confirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      console.log('Starting customer delete process...');
      console.log('Deleting customer:', customerToDelete.id);

      const response = await api.delete(`/api/customers/${customerToDelete.id}`);
      console.log('Customer deleted successfully:', response.data);

      setShowDeleteModal(false);
      setSuccessMessage('تم حذف العميل بنجاح');
      setShowSuccessModal(true);
      fetchCustomers();
    } catch (error: any) {
      console.error('Error deleting customer:', error);

      if (error.response?.status === 401) {
        console.log('401 error during delete - auth will be handled by axios interceptor');
        // The axios interceptor will handle token refresh or redirect to login
      } else if (error.response?.status === 400) {
        console.error('Cannot delete customer:', error.response.data);
        // Handle specific delete errors (e.g., customer has unpaid debts)
      } else {
        console.error('Unexpected error:', error.message);
      }
    }
  };

  // Remove client-side filtering since it's handled in fetchCustomers
  const displayedCustomers = customers;

  // Show loading while auth is initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري التحقق من الجلسة...</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحميل العملاء...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm"
              >
                <FaArrowLeft />
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center">
                <FaUsers className="ml-3 text-primary-600 dark:text-primary-400" />
                إدارة العملاء
              </h1>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              <button
                onClick={() => fetchCustomers()}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2 rounded-md hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm"
                title="تحديث البيانات"
              >
                <FaSync className={loading ? 'animate-spin' : ''} />
              </button>
              <button
                onClick={handleAddCustomer}
                className="bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg"
              >
                <FaPlus className="ml-2" />
                <span className="hidden sm:inline">إضافة عميل جديد</span>
                <span className="sm:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Customers */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUsers className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي العملاء</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{stats.totalCustomers}</div>
            </div>
          </div>

          {/* Active Customers */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaUser className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">العملاء النشطين</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">{stats.activeCustomers}</div>
            </div>
          </div>

          {/* Total Debts */}
          <div className="flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30">
            <div className="bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-danger-600 dark:text-danger-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-danger-700 dark:text-danger-300 mb-1">إجمالي الديون</div>
              <div className="text-lg font-bold text-danger-600 dark:text-danger-400 truncate">
                {stats.totalDebts.toFixed(2)} د.ل
              </div>
              <div className="text-xs text-danger-500 dark:text-danger-400">المبالغ المستحقة</div>
            </div>
          </div>

          {/* Total Paid Sales */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaShoppingCart className="text-blue-600 dark:text-blue-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">المبيعات المدفوعة</div>
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">
                {stats.totalPaidSales.toFixed(2)} د.ل
              </div>
              <div className="text-xs text-blue-500 dark:text-blue-400">المبالغ المدفوعة فعلياً</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={handleSearch}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectBox
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value) => {
                    const newValue = parseInt(value, 10);
                    setItemsPerPage(newValue);
                  }}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                />
              </div>

              {/* Active Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  العملاء النشطين
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="activeOnly"
                    checked={showActiveOnly}
                    onChange={(checked) => {
                      setShowActiveOnly(checked);
                      if (checked) setShowInactiveOnly(false);
                    }}
                    label="النشطين فقط"
                  />
                </div>
              </div>

              {/* Inactive Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  العملاء غير النشطين
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="inactiveOnly"
                    checked={showInactiveOnly}
                    onChange={(checked) => {
                      setShowInactiveOnly(checked);
                      if (checked) setShowActiveOnly(false);
                    }}
                    label="غير النشطين فقط"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="sm:col-span-2 lg:col-span-1 flex flex-col gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm('');
                    setShowActiveOnly(false);
                    setShowInactiveOnly(false);
                    setItemsPerPage(10);
                    setCurrentPage(1);
                    fetchCustomers(1, 10, false, false, '');
                  }}
                  className="bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm"
                >
                  إعادة تعيين
                </button>
                <button
                  type="button"
                  onClick={() => {
                    fetchCustomers(1, itemsPerPage);
                  }}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Connection Error Alert */}
      {connectionError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3 flex-1">
              <p className="text-sm text-red-800 dark:text-red-200">
                {connectionError}
              </p>
              {connectionError.includes('لا يمكن الاتصال بالخادم') && (
                <button
                  onClick={() => fetchCustomers()}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
                >
                  إعادة المحاولة
                </button>
              )}
            </div>
            <div className="mr-auto">
              <button
                onClick={() => setConnectionError(null)}
                className="text-red-400 hover:text-red-600 dark:hover:text-red-300"
              >
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Customers List */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700">
        {displayedCustomers.length === 0 ? (
          <div className="text-center py-12">
            <FaUsers className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">لا توجد عملاء</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    معلومات الاتصال
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المديونية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبيعات المدفوعة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {displayedCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FaUser className="text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {customer.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            #{customer.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      <div className="space-y-1">
                        {customer.phone && (
                          <div className="flex items-center">
                            <FaPhone className="text-gray-400 ml-2" />
                            {customer.phone}
                          </div>
                        )}
                        {customer.email && (
                          <div className="flex items-center">
                            <FaEnvelope className="text-gray-400 ml-2" />
                            {customer.email}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaMoneyBillWave className={`ml-2 ${customer.total_debt > 0 ? 'text-red-500' : 'text-green-500'}`} />
                        <span className={`text-sm font-medium ${customer.total_debt > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                          {customer.total_debt.toFixed(2)} د.ل
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaShoppingCart className="text-blue-500 ml-2" />
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {customer.total_sales.toFixed(2)} د.ل
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        customer.is_active
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {customer.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => navigate(`/debts?customer_id=${customer.id}`)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                          title="عرض المديونية"
                        >
                          <FaEye />
                        </button>
                        <button
                          onClick={() => handleEditCustomer(customer)}
                          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                          title="تعديل"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDeleteCustomer(customer)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                          title="حذف"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {!loading && displayedCustomers.length > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => {
                  const newPage = Math.max(1, pagination.page - 1);
                  setCurrentPage(newPage);
                  fetchCustomers(newPage, itemsPerPage);
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page <= 1
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                السابق
              </button>
              <button
                onClick={() => {
                  const newPage = Math.min(pagination.pages, pagination.page + 1);
                  setCurrentPage(newPage);
                  fetchCustomers(newPage, itemsPerPage);
                }}
                disabled={pagination.page >= pagination.pages}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.pages
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                  <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                  <button
                    onClick={() => {
                      const newPage = Math.max(1, pagination.page - 1);
                      setCurrentPage(newPage);
                      fetchCustomers(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">السابق</span>
                    <FaChevronRight className="h-5 w-5" />
                  </button>

                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (pagination.pages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      // If near start, show first 5
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      // If near end, show last 5
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      // Otherwise show current and 2 on each side
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          setCurrentPage(pageNum);
                          fetchCustomers(pageNum, itemsPerPage);
                        }}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => {
                      const newPage = Math.min(pagination.pages, pagination.page + 1);
                      setCurrentPage(newPage);
                      fetchCustomers(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">التالي</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Customer Form Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={editingCustomer ? 'تعديل العميل' : 'إضافة عميل جديد'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-5">
          {/* Basic Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaUser className="ml-2 text-primary-600 dark:text-primary-400" />
              المعلومات الأساسية
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <TextInput
                label="اسم العميل"
                name="name"
                value={formData.name}
                onChange={(value) => {
                  setFormData({ ...formData, name: value });
                  if (formErrors.name) {
                    setFormErrors({ ...formErrors, name: '' });
                  }
                }}
                placeholder="أدخل اسم العميل"
                required
                error={formErrors.name}
                icon={<FaUser />}
                maxLength={100}
              />

              <TextInput
                label="رقم الهاتف"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={(value) => setFormData({ ...formData, phone: value })}
                placeholder="أدخل رقم الهاتف"
                icon={<FaPhone />}
                maxLength={20}
              />
            </div>

            <div className="mt-4">
              <TextInput
                label="البريد الإلكتروني"
                name="email"
                type="email"
                value={formData.email}
                onChange={(value) => {
                  setFormData({ ...formData, email: value });
                  if (formErrors.email) {
                    setFormErrors({ ...formErrors, email: '' });
                  }
                }}
                placeholder="أدخل البريد الإلكتروني (اختياري)"
                error={formErrors.email}
                icon={<FaEnvelope />}
                maxLength={100}
              />
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaMapMarkerAlt className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات إضافية
            </h3>
            <div className="space-y-4">
              <TextArea
                label="العنوان"
                name="address"
                value={formData.address}
                onChange={(value) => setFormData({ ...formData, address: value })}
                placeholder="أدخل العنوان (اختياري)"
                rows={2}
                maxLength={200}
              />

              <TextArea
                label="ملاحظات"
                name="notes"
                value={formData.notes}
                onChange={(value) => setFormData({ ...formData, notes: value })}
                placeholder="أدخل ملاحظات إضافية (اختياري)"
                rows={2}
                maxLength={500}
              />
            </div>
          </div>

          {/* Status Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <FaToggleOn className="ml-2 text-primary-600 dark:text-primary-400" />
              حالة العميل
            </h3>
            <ToggleSwitch
              id="is_active"
              checked={formData.is_active}
              onChange={(checked) => setFormData({ ...formData, is_active: checked })}
              label="عميل نشط"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              تحديد ما إذا كان العميل نشط أم لا
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowModal(false)}
              disabled={loading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              <span>إلغاء</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center justify-center min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  <span>جاري الحفظ...</span>
                </>
              ) : (
                <>
                  {editingCustomer ? <FaEdit className="ml-2" /> : <FaPlus className="ml-2" />}
                  <span>{editingCustomer ? 'تحديث العميل' : 'إضافة العميل'}</span>
                </>
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        title="تأكيد الحذف"
        message={`هل أنت متأكد من حذف العميل؟`}
        itemName={customerToDelete?.name || ''}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="نجح العملية"
        message={successMessage}
      />
    </div>
  );
};

export default Customers;
