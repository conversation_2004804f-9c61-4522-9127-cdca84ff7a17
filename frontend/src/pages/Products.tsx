import React, { useEffect, useState } from 'react';
import useProductStore from '../stores/productStore';
import ProductForm from '../components/ProductForm';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaFilter,
  FaArrowLeft,
  FaSync,
  FaExclamationTriangle,
  FaChevronLeft,
  FaChevronRight,
  FaBox
} from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import SelectBox from '../components/SelectBox';
import ToggleSwitch from '../components/ToggleSwitch';
import Modal from '../components/Modal';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import SuccessModal from '../components/SuccessModal';

interface ProductsProps {
  isNew?: boolean;
}

const Products: React.FC<ProductsProps> = ({ isNew = false }) => {
  const {
    products,
    categories,
    loading,
    error,
    filters,
    selectedProduct,
    pagination,
    fetchProducts,
    fetchCategories,
    deleteProduct,
    setFilters,
    setSelectedProduct,
    nextPage,
    prevPage,
    setPage,
    setLimit
  } = useProductStore();

  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const navigate = useNavigate();
  const location = useLocation();

  const [showForm, setShowForm] = useState(isNew);
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [selectedCategory, setSelectedCategory] = useState(filters.category || '');
  const [showLowStock, setShowLowStock] = useState(filters.lowStock || false);
  const [showZeroStock, setShowZeroStock] = useState(filters.zeroStock || false);
  const [isActive, setIsActive] = useState<boolean | undefined>(filters.isActive);
  const [showFilters, setShowFilters] = useState(false);

  const [itemsPerPage, setItemsPerPage] = useState(filters.limit || 10);

  // حالات النوافذ
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    productId: number | null;
    productName: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    productId: null,
    productName: '',
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
  }>({
    isOpen: false,
    title: '',
    message: ''
  });

  useEffect(() => {
    // Apply existing filters when component mounts
    console.log('Component mounted, applying filters:', filters);
    console.log('Filter details on mount - search:', filters.search || 'none',
                'category:', filters.category || 'none',
                'lowStock:', filters.lowStock || false);

    // Check if we have state from navigation
    const locationState = location.state as { zeroStock?: boolean, search?: string } | null;

    if (locationState) {
      let newFilters = { ...filters };
      let filtersUpdated = false;

      if (locationState.zeroStock) {
        console.log('Applying zero stock filter from navigation state');
        setShowZeroStock(true);
        newFilters = {
          ...newFilters,
          zeroStock: true,
          page: 1,
          limit: 10
        };
        filtersUpdated = true;
      }

      if (locationState.search) {
        console.log('Applying search filter from navigation state:', locationState.search);
        setSearchTerm(locationState.search);
        newFilters = {
          ...newFilters,
          search: locationState.search,
          page: 1
        };
        filtersUpdated = true;
      }

      if (filtersUpdated) {
        setFilters(newFilters);
      } else {
        // Fetch products with current filters
        fetchProducts(filters);
      }
    } else {
      // Fetch products with current filters
      fetchProducts(filters);
    }

    fetchCategories();

    // Show form when isNew is true
    if (isNew) {
      setSelectedProduct(null);
      setShowForm(true);
    }

    // No automatic refresh - removed auto-refresh functionality
    return () => {};
  }, [isNew, location.state]);

  // Update local state when filters change
  useEffect(() => {
    console.log('Filters changed in store:', filters);
    setSearchTerm(filters.search || '');
    setSelectedCategory(filters.category || '');
    setShowLowStock(filters.lowStock || false);
    setShowZeroStock(filters.zeroStock || false);
    setIsActive(filters.isActive);
    setItemsPerPage(filters.limit || 10);
  }, [filters]);



  const handleDelete = (id: number, productName: string) => {
    setDeleteModal({
      isOpen: true,
      productId: id,
      productName: productName,
      isLoading: false
    });
  };

  const confirmDelete = async () => {
    if (!deleteModal.productId) return;

    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      await deleteProduct(deleteModal.productId);

      // إغلاق نافذة الحذف
      setDeleteModal({
        isOpen: false,
        productId: null,
        productName: '',
        isLoading: false
      });

      // عرض نافذة النجاح
      setSuccessModal({
        isOpen: true,
        title: 'تم الحذف بنجاح',
        message: `تم حذف المنتج "${deleteModal.productName}" بنجاح`
      });

      // إعادة تحميل المنتجات
      fetchProducts(filters);
    } catch (error: any) {
      // إغلاق نافذة الحذف
      setDeleteModal({
        isOpen: false,
        productId: null,
        productName: '',
        isLoading: false
      });

      // عرض رسالة الخطأ (ستظهر في error state من المتجر)
      console.error('Error deleting product:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm"
              >
                <FaArrowLeft />
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center">
                <FaBox className="ml-3 text-primary-600 dark:text-primary-400" />
                إدارة المنتجات
              </h1>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              <button
                onClick={() => fetchProducts()}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2 rounded-md hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm"
                title="تحديث"
              >
                <FaSync className={loading ? "animate-spin" : ""} />
              </button>
              <button
                onClick={() => {
                  setSelectedProduct(null);
                  setShowForm(true);
                }}
                className="bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg"
              >
                <FaPlus className="ml-2" />
                <span className="hidden sm:inline">إضافة منتج جديد</span>
                <span className="sm:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Products */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaBox className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المنتجات</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{products.length}</div>
            </div>
          </div>

          {/* Active Products */}
          <div className="flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30">
            <div className="bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaBox className="text-success-600 dark:text-success-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-success-700 dark:text-success-300 mb-1">المنتجات النشطة</div>
              <div className="text-xl font-bold text-success-600 dark:text-success-400">
                {products.filter(p => p.is_active).length}
              </div>
            </div>
          </div>

          {/* Low Stock Products */}
          <div className="flex items-center gap-3 p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg border border-warning-100 dark:border-warning-800/30">
            <div className="bg-warning-100 dark:bg-warning-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaExclamationTriangle className="text-warning-600 dark:text-warning-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-warning-700 dark:text-warning-300 mb-1">مخزون منخفض</div>
              <div className="text-xl font-bold text-warning-600 dark:text-warning-400">
                {products.filter(p => p.quantity <= p.min_quantity && p.quantity > 0).length}
              </div>
            </div>
          </div>

          {/* Out of Stock Products */}
          <div className="flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30">
            <div className="bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaExclamationTriangle className="text-danger-600 dark:text-danger-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-danger-700 dark:text-danger-300 mb-1">نفدت الكمية</div>
              <div className="text-xl font-bold text-danger-600 dark:text-danger-400">
                {products.filter(p => p.quantity === 0).length}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search & Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث عن منتج..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyUp={(e) => {
                  if (e.key === 'Enter') {
                    console.log('Applying search filter on Enter key:', searchTerm);
                    const newFilters = {
                      search: searchTerm,
                      category: selectedCategory,
                      lowStock: showLowStock
                    };
                    console.log('New filters to apply on Enter:', newFilters);
                    setFilters(newFilters);
                  }
                }}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={() => fetchProducts(filters)}
              className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
              title="تحديث"
            >
              <FaSync className={`ml-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
            <button
              onClick={() => {
                console.log('Applying search filter on button click:', searchTerm);
                const newFilters = {
                  search: searchTerm,
                  category: selectedCategory,
                  lowStock: showLowStock
                };
                setFilters(newFilters);
              }}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4">
              {/* Category Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectBox
                  label="الفئة"
                  name="category"
                  value={selectedCategory}
                  onChange={(value) => {
                    setSelectedCategory(value);
                  }}
                  options={[
                    { value: '', label: 'جميع الفئات' },
                    ...categories.map(category => ({
                      value: category,
                      label: category
                    }))
                  ]}
                />
              </div>

              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <SelectBox
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value) => {
                    const newValue = parseInt(value, 10);
                    setItemsPerPage(newValue);
                    setLimit(newValue);
                  }}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                />
              </div>

              {/* Low Stock Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مخزون منخفض
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="lowStock"
                    checked={showLowStock}
                    onChange={(checked) => setShowLowStock(checked)}
                    label="منخفض فقط"
                  />
                </div>
              </div>

              {/* Zero Stock Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  نفدت الكمية
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="zeroStock"
                    checked={showZeroStock}
                    onChange={(checked) => setShowZeroStock(checked)}
                    label="صفر فقط"
                  />
                </div>
              </div>

              {/* Active Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  المنتجات النشطة
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center">
                  <ToggleSwitch
                    id="activeStatus"
                    checked={isActive === true}
                    onChange={(checked) => setIsActive(checked ? true : undefined)}
                    label="النشطة فقط"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="sm:col-span-2 lg:col-span-1 flex flex-col gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('');
                    setShowLowStock(false);
                    setShowZeroStock(false);
                    setIsActive(undefined);
                    setItemsPerPage(10);
                    const emptyFilters = { limit: 10 };
                    setFilters(emptyFilters);
                    console.log('Filters reset to empty');
                  }}
                  className="bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm"
                >
                  إعادة تعيين
                </button>
                <button
                  type="button"
                  onClick={() => {
                    console.log('Applying all filters');
                    const newFilters = {
                      search: searchTerm,
                      category: selectedCategory,
                      lowStock: showLowStock,
                      zeroStock: showZeroStock,
                      isActive: isActive,
                      limit: itemsPerPage,
                      page: 1
                    };
                    console.log('New filters to apply:', newFilters);
                    setFilters(newFilters);
                    console.log('Current pagination state:', pagination);
                  }}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-danger-100 dark:bg-danger-900/30 border border-danger-200 dark:border-danger-800 text-danger-700 dark:text-danger-300 px-4 py-3 rounded-xl mb-6">
          {error}
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden">
        <div className="overflow-x-auto custom-scrollbar">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  اسم المنتج
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الفئة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  السعر
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  المخزون
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    جاري التحميل...
                  </td>
                </tr>
              ) : products.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    لم يتم العثور على منتجات
                  </td>
                </tr>
              ) : (
                products.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {product.barcode || 'بدون باركود'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {product.category || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-gray-100">
                        {product.price.toFixed(2)} د.ل
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        هامش الربح: {product.profit_margin.toFixed(1)}%
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className={`text-sm ${
                          product.quantity === 0
                            ? 'text-danger-600 dark:text-danger-400 font-medium'
                            : product.quantity <= product.min_quantity
                            ? 'text-warning-600 dark:text-warning-400 font-medium'
                            : 'text-gray-900 dark:text-gray-100'
                        }`}
                      >
                        {product.quantity} {product.unit}
                      </div>
                      {product.quantity === 0 ? (
                        <div className="text-xs text-danger-500 dark:text-danger-400 flex items-center">
                          <FaExclamationTriangle className="mr-1" /> نفدت الكمية
                        </div>
                      ) : product.quantity <= product.min_quantity ? (
                        <div className="text-xs text-warning-500 dark:text-warning-400 flex items-center">
                          <FaExclamationTriangle className="mr-1" /> مخزون منخفض
                        </div>
                      ) : null}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          product.is_active
                            ? 'bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300'
                            : 'bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300'
                        }`}
                      >
                        {product.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowForm(true);
                        }}
                        className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 ml-3"
                        title="تعديل"
                      >
                        <FaEdit className="inline text-lg" />
                      </button>
                      <button
                        onClick={() => handleDelete(product.id, product.name)}
                        className="text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-300"
                        title="حذف"
                      >
                        <FaTrash className="inline text-lg" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && products.length > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => {
                  console.log(`Moving to previous page from ${pagination.page}`);
                  prevPage();
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page <= 1
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                السابق
              </button>

              <div className="text-xs text-gray-500 dark:text-gray-400">
                <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>
              </div>

              <button
                onClick={() => {
                  console.log(`Moving to next page from ${pagination.page}`);
                  nextPage();
                }}
                disabled={pagination.page >= pagination.pages}
                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.pages
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                  <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                  <button
                    onClick={() => {
                      console.log(`Moving to previous page from ${pagination.page}`);
                      prevPage();
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">السابق</span>
                    <FaChevronRight className="h-5 w-5" />
                  </button>

                  {/* Page info - Only shown at the bottom of the pagination */}
                  <div className="hidden text-xs text-gray-500 dark:text-gray-400 px-2 py-1 border-r border-gray-300 dark:border-gray-600">
                    صفحة {pagination.page} من {pagination.pages}
                  </div>

                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (pagination.pages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      // If near start, show first 5
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      // If near end, show last 5
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      // Otherwise show current and 2 on each side
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          console.log(`Setting page to ${pageNum}`);
                          setPage(pageNum);
                        }}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => {
                      console.log(`Moving to next page from ${pagination.page}`);
                      nextPage();
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">التالي</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>



      {/* Product Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={() => {
          setSelectedProduct(null);
          setShowForm(false);
        }}
        title={selectedProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        size="lg"
      >
        <ProductForm
          product={selectedProduct || undefined}
          onClose={() => {
            setSelectedProduct(null);
            setShowForm(false);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({
          isOpen: false,
          productId: null,
          productName: '',
          isLoading: false
        })}
        onConfirm={confirmDelete}
        title="تأكيد حذف المنتج"
        message="هل أنت متأكد من رغبتك في حذف هذا المنتج؟"
        itemName={deleteModal.productName}
        isLoading={deleteModal.isLoading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({
          isOpen: false,
          title: '',
          message: ''
        })}
        title={successModal.title}
        message={successModal.message}
      />
    </div>
  );
};

export default Products;