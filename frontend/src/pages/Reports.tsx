import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  FaChartBar,
  FaChartLine,
  FaChartPie,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarAlt,
  FaCalendar,
  FaDownload,
  FaPrint,
  FaArrowUp,
  FaArrowDown,
  FaArrowLeft,
  FaShoppingCart,
  FaSync,
  FaServer,
  FaUsers,
  FaDatabase,
  FaClock,
  FaHdd,
  FaUserCheck,
  FaCog,
  FaTrash,
  FaUndo,
  FaExclamationTriangle
} from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import ReactApexChart from 'react-apexcharts';
import { useReportsStore } from '../stores/reportsStore';
import { useAuthStore } from '../stores/authStore';
import { ApexOptions } from 'apexcharts';
import { getCurrentTripoliDateTime } from '../services/dateTimeService';
import DatePicker from '../components/DatePicker';
import { ReportType, ReportPeriod } from '../stores/reportsStore';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import RestoreConfirmModal from '../components/RestoreConfirmModal';
import SuccessModal from '../components/SuccessModal';
import ConfirmModal from '../components/ConfirmModal';
import AllBackupsModal from '../components/AllBackupsModal';
import AuthDebug from '../components/AuthDebug';

const Reports: React.FC = () => {
  const { type } = useParams<{ type?: string }>();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDark = theme === 'dark';



  // استخدام متجر البيانات
  const {
    salesTrends,
    previousPeriodSales,
    previousPeriodTotal,
    productCategories,
    inactiveProducts,
    inventoryStatus,
    systemStats,
    isLoading,
    selectedPeriod,
    selectedReportType,
    fetchSalesTrends,
    fetchPreviousPeriodTotal,
    fetchProductCategories,
    fetchInventoryStatus,
    fetchSystemStats,
    fetchDashboardStats,
    setReportType,
    setPeriod
  } = useReportsStore();

  // استخدام متجر المصادقة في المستوى الأعلى
  const { user: currentUser, token: authToken } = useAuthStore();



  // متغيرات النسخ الاحتياطية
  const [backups, setBackups] = useState<any[]>([]);
  const [backupsLoading, setBackupsLoading] = useState(false);

  // متغيرات النوافذ
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    backupName: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    backupName: '',
    isLoading: false
  });

  const [restoreModal, setRestoreModal] = useState<{
    isOpen: boolean;
    backupInfo: any;
    isLoading: boolean;
  }>({
    isOpen: false,
    backupInfo: null,
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    details: any;
    autoClose: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    details: null,
    autoClose: false
  });

  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    type: 'backup' | 'update' | 'clear-cache';
    title: string;
    message: string;
    description?: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    type: 'backup',
    title: '',
    message: '',
    description: '',
    isLoading: false
  });

  const [allBackupsModal, setAllBackupsModal] = useState(false);

  // جلب قائمة النسخ الاحتياطية
  const fetchBackups = async () => {
    try {
      setBackupsLoading(true);
      const response = await fetch('/api/dashboard/backups');
      const data = await response.json();

      if (response.ok) {
        setBackups(data.backups || []);
      } else {
        console.error('Error fetching backups:', data);
        setBackups([]);
      }
    } catch (error) {
      console.error('Error fetching backups:', error);
      setBackups([]);
    } finally {
      setBackupsLoading(false);
    }
  };

  // فتح نافذة تأكيد الحذف
  const openDeleteModal = (backupName: string) => {
    setDeleteModal({
      isOpen: true,
      backupName,
      isLoading: false
    });
  };

  // تأكيد حذف النسخة الاحتياطية
  const confirmDeleteBackup = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch(`/api/dashboard/backups/${deleteModal.backupName}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // إغلاق نافذة الحذف
        setDeleteModal({ isOpen: false, backupName: '', isLoading: false });

        // عرض نافذة النجاح
        setSuccessModal({
          isOpen: true,
          title: 'تم الحذف بنجاح',
          message: 'تم حذف النسخة الاحتياطية بنجاح',
          details: { backup_name: deleteModal.backupName },
          autoClose: true
        });

        // تحديث القائمة والإحصائيات
        fetchBackups();
        fetchSystemStats();
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error deleting backup:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الحذف',
        message: `فشل في حذف النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد الاستعادة
  const openRestoreModal = (backupName: string) => {
    const backupInfo = backups.find(backup => backup.name === backupName);
    setRestoreModal({
      isOpen: true,
      backupInfo,
      isLoading: false
    });
  };

  // تأكيد استعادة النسخة الاحتياطية
  const confirmRestoreBackup = async () => {
    if (!restoreModal.backupInfo) return;

    setRestoreModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch(`/api/dashboard/restore-backup/${restoreModal.backupInfo.name}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // إغلاق نافذة الاستعادة
        setRestoreModal({ isOpen: false, backupInfo: null, isLoading: false });

        // عرض نافذة النجاح مع إعادة تحميل تلقائية
        setSuccessModal({
          isOpen: true,
          title: 'تم الاستعادة بنجاح',
          message: 'تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل النظام خلال 3 ثوانٍ لتطبيق التغييرات.',
          details: {
            backup_name: data.backup_name,
            size: data.size,
            restored_at: data.restored_at,
            backup_before_restore: data.backup_before_restore
          },
          autoClose: true
        });

        // إعادة تحميل الصفحة بعد 3 ثوانٍ
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error restoring backup:', error);
      setRestoreModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الاستعادة',
        message: `فشل في استعادة النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // متغيرات مبيعات المستخدمين اليومية
  const [dailyUserSales, setDailyUserSales] = useState<any>(null);
  const [dailyUserSalesLoading, setDailyUserSalesLoading] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState(() => {
    // استخدام خدمة التاريخ للحصول على التاريخ الحالي بتوقيت طرابلس
    const tripoliDate = getCurrentTripoliDateTime();
    const year = tripoliDate.getFullYear();
    const month = (tripoliDate.getMonth() + 1).toString().padStart(2, '0');
    const day = tripoliDate.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  });

  // جلب بيانات مبيعات المستخدمين اليومية
  const fetchDailyUserSales = async (date?: string, forceRefresh = false) => {
    try {
      const targetDate = date || selectedDate;

      // منع الاستدعاءات المتكررة للتاريخ نفسه
      if (dailyUserSalesLoading) {
        return;
      }

      // التحقق من وجود البيانات للتاريخ نفسه (إلا في حالة التحديث القسري)
      if (!forceRefresh && dailyUserSales && dailyUserSales.date === targetDate) {
        return;
      }

      setDailyUserSalesLoading(true);

      if (!currentUser || currentUser.role !== 'admin') {
        setDailyUserSales(null);
        setDailyUserSalesLoading(false);
        return;
      }

      const url = `/api/dashboard/daily-user-sales?date=${targetDate}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok) {
        setDailyUserSales(data);
        setLastUpdateTime(new Date());
      } else {
        setDailyUserSales(null);
      }
    } catch (error) {
      console.error('Error fetching daily user sales:', error);
      setDailyUserSales(null);
    } finally {
      setDailyUserSalesLoading(false);
    }
  };

  // تعيين نوع التقرير عند تغيير المسار
  useEffect(() => {
    if (type && (type === 'sales' || type === 'products' || type === 'inventory' || type === 'customers' || type === 'system' || type === 'daily-users')) {
      setReportType(type as ReportType);
    }
  }, [type, setReportType]);

  // جلب البيانات عند تحميل الصفحة أو تغيير الفترة أو نوع التقرير
  useEffect(() => {
    if (selectedReportType === 'sales') {
      fetchSalesTrends(selectedPeriod);
      fetchPreviousPeriodTotal(selectedPeriod); // جلب البيانات الفعلية للفترة السابقة
      fetchDashboardStats();
    } else if (selectedReportType === 'products') {
      fetchProductCategories();
    } else if (selectedReportType === 'inventory') {
      fetchInventoryStatus();
    } else if (selectedReportType === 'system') {
      fetchSystemStats();
      fetchBackups();
    } else if (selectedReportType === 'daily-users') {
      fetchDailyUserSales(selectedDate);
    }
  }, [selectedReportType, selectedPeriod, selectedDate, fetchSalesTrends, fetchPreviousPeriodTotal, fetchProductCategories, fetchInventoryStatus, fetchSystemStats, fetchDashboardStats]);

  // جلب بيانات مبيعات المستخدمين عند تحميل الصفحة لأول مرة إذا كانت التبويبة نشطة
  useEffect(() => {
    if (selectedReportType === 'daily-users' && selectedDate) {
      // التحقق من أن المستخدم مدير قبل جلب البيانات
      if (currentUser && currentUser.role === 'admin') {
        fetchDailyUserSales(selectedDate);
      }
    }
  }, []); // تشغيل مرة واحدة فقط عند تحميل الصفحة

  // تغيير نوع التقرير والانتقال إلى المسار المناسب
  const handleReportTypeChange = (newType: ReportType) => {
    setReportType(newType);
    navigate(`/reports/${newType}`);
  };

  // تغيير الفترة الزمنية
  const handlePeriodChange = (period: ReportPeriod) => {
    setPeriod(period);
  };



  // إعداد مخطط المبيعات المتقدم باستخدام ApexCharts
  const renderSalesChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = salesTrends.map(item => ({
      date: item.date,
      amount: item.amount
    }));

    // حساب إحصائيات المبيعات المتقدمة
    const totalSales = chartData.reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

    // فلترة القيم الصحيحة فقط (أكبر من 0) لحساب الحد الأدنى والأقصى
    const validAmounts = chartData
      .map(item => typeof item.amount === 'number' ? item.amount : 0)
      .filter(amount => amount > 0);

    const maxSale = validAmounts.length > 0 ? Math.max(...validAmounts) : 0;
    const minSale = validAmounts.length > 0 ? Math.min(...validAmounts) : 0;
    const avgSale = chartData.length > 0 ? totalSales / chartData.length : 0;

    // تسجيل البيانات للتشخيص
    console.log('Sales Statistics:', {
      chartDataLength: chartData.length,
      validAmountsLength: validAmounts.length,
      totalSales,
      maxSale,
      minSale,
      avgSale,
      validAmounts: validAmounts.slice(0, 5) // أول 5 قيم للمراجعة
    });

    // حساب الانحراف المعياري
    const variance = chartData.reduce((sum, item) => {
      const diff = item.amount - avgSale;
      return sum + (diff * diff);
    }, 0) / chartData.length;
    const standardDeviation = Math.sqrt(variance);

    // حساب معدل النمو بناءً على مقارنة الفترة الحالية مع فترة سابقة محسوبة من البيانات الفعلية
    const calculateGrowthRate = () => {
      if (chartData.length === 0) return { rate: 0, currentTotal: 0, previousTotal: 0, hasData: false };

      // حساب إجمالي مبيعات الفترة الحالية
      const currentPeriodTotal = chartData.reduce((sum, item) =>
        sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

      // استخدام البيانات الفعلية للفترة السابقة من قاعدة البيانات
      // هذه البيانات يتم جلبها من API endpoint جديد يحسب الفترة السابقة الفعلية
      const actualPreviousPeriodTotal = previousPeriodTotal; // البيانات الفعلية من قاعدة البيانات

      // تسجيل البيانات للتشخيص
      console.log('Growth Rate Calculation:', {
        chartDataLength: chartData.length,
        currentPeriodTotal,
        actualPreviousPeriodTotal,
        selectedPeriod
      });

      // إذا كانت مبيعات الفترة السابقة صفر، نعتبر النمو 100% إذا كان هناك مبيعات حالية
      if (actualPreviousPeriodTotal === 0) {
        const rate = currentPeriodTotal > 0 ? 100 : 0;
        return { rate, currentTotal: currentPeriodTotal, previousTotal: 0, hasData: true };
      }

      // حساب معدل النمو: ((الفترة الحالية - الفترة السابقة) / الفترة السابقة) × 100
      const rate = ((currentPeriodTotal - actualPreviousPeriodTotal) / actualPreviousPeriodTotal) * 100;

      return { rate, currentTotal: currentPeriodTotal, previousTotal: actualPreviousPeriodTotal, hasData: true };
    };

    const growthData = calculateGrowthRate();
    const growthRate = growthData.rate;

    // تحديد نوع المخطط بناءً على الفترة لعرض أفضل
    const chartType = selectedPeriod === 'day' ? 'line' :
                     selectedPeriod === 'week' ? 'area' :
                     selectedPeriod === 'month' ? 'bar' : 'line';

    // استخدام بيانات الفترة السابقة الحقيقية لخط الاتجاه
    const previousPeriodData = previousPeriodSales.map(item => {
      const amount = typeof item.amount === 'number' ? item.amount : 0;
      return Math.round(amount * 100) / 100;
    });

    // إعداد خيارات المخطط المتقدم
    const options: ApexOptions = {
      chart: {
        type: chartType as any,
        fontFamily: 'Tajawal, sans-serif',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: false,
            reset: true
          },
          export: {
            csv: {
              filename: 'تقرير_المبيعات',
              columnDelimiter: ',',
              headerCategory: 'التاريخ',
              headerValue: 'القيمة'
            },
            svg: {
              filename: 'مخطط_المبيعات'
            },
            png: {
              filename: 'مخطط_المبيعات'
            }
          }
        },
        zoom: {
          enabled: true,
          type: 'x',
          autoScaleYaxis: true
        },
        animations: {
          enabled: true,
          speed: 1000,
          animateGradually: {
            enabled: true,
            delay: 200
          },
          dynamicAnimation: {
            enabled: true,
            speed: 400
          }
        },
        background: 'transparent',
        locales: [{
          name: 'ar',
          options: {
            months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
            days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
            toolbar: {
              exportToSVG: "تحميل SVG",
              exportToPNG: "تحميل PNG",
              exportToCSV: "تحميل CSV",
              selection: "التحديد",
              selectionZoom: "تكبير التحديد",
              zoomIn: "تكبير",
              zoomOut: "تصغير",
              pan: "تحريك",
              reset: "إعادة تعيين التكبير"
            }
          }
        }],
        defaultLocale: 'ar'
      },
      colors: chartType === 'bar' ?
        (isDark ? ['#10B981', 'rgba(248, 113, 113, 0.7)'] : ['#059669', 'rgba(220, 38, 38, 0.7)']) :
        (isDark ? ['#3B82F6', 'rgba(248, 113, 113, 0.7)'] : ['#1D4ED8', 'rgba(220, 38, 38, 0.7)']),
      fill: {
        type: chartType === 'bar' ? 'solid' : 'gradient',
        gradient: {
          shade: isDark ? 'dark' : 'light',
          shadeIntensity: 0.8,
          opacityFrom: chartType === 'area' ? 0.7 : 0.9,
          opacityTo: chartType === 'area' ? 0.1 : 0.2,
          stops: [0, 90, 100],
          colorStops: [
            [
              {
                offset: 0,
                color: isDark ? '#3B82F6' : '#1D4ED8',
                opacity: chartType === 'area' ? 0.7 : 0.9
              },
              {
                offset: 100,
                color: isDark ? '#1E40AF' : '#1E3A8A',
                opacity: chartType === 'area' ? 0.1 : 0.2
              }
            ],
            [
              {
                offset: 0,
                color: isDark ? '#F87171' : '#DC2626',
                opacity: chartType === 'area' ? 0.5 : 0.7
              },
              {
                offset: 100,
                color: isDark ? '#DC2626' : '#B91C1C',
                opacity: chartType === 'area' ? 0.1 : 0.2
              }
            ]
          ]
        }
      },
      dataLabels: {
        enabled: chartType === 'bar',
        style: {
          fontSize: '12px',
          fontWeight: 'bold',
          colors: [isDark ? '#F9FAFB' : '#fff']
        },
        formatter: (value: number) => {
          return value >= 1000 ? `${(value / 1000).toFixed(1)}K` : value.toFixed(0);
        }
      },
      stroke: {
        curve: chartType === 'line' ? 'smooth' : 'straight',
        width: chartType === 'bar' ? 0 : [4, 3], // خط أكثر سمكاً للمبيعات الفعلية، أقل للفترة السابقة
        dashArray: [0, 8] // خط متصل للمبيعات الفعلية، خط متقطع للفترة السابقة
      },
      grid: {
        borderColor: isDark ? 'rgba(156, 163, 175, 0.3)' : 'rgba(107, 114, 128, 0.1)',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: isDark ? 0.3 : 0.5
        },
        padding: {
          top: 0,
          right: 0,
          bottom: 0,
          left: 10
        },
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      markers: {
        size: [6, 5], // نقاط أكبر للمبيعات الفعلية
        colors: [
          isDark ? '#1F2937' : '#FFFFFF', // خلفية بيضاء/رمادية للمبيعات الفعلية
          isDark ? '#1F2937' : '#FFFFFF'  // خلفية بيضاء/رمادية للفترة السابقة
        ],
        strokeColors: [
          isDark ? '#3B82F6' : '#1D4ED8', // حدود زرقاء للمبيعات الفعلية
          isDark ? '#F87171' : '#DC2626'  // حدود حمراء للفترة السابقة
        ],
        strokeWidth: [3, 3], // حدود متساوية وواضحة
        shape: ['circle', 'circle'], // أشكال دائرية للجميع
        hover: {
          size: 9, // حجم أكبر عند التمرير
          sizeOffset: 4
        }
      },
      xaxis: {
        categories: chartData.map(item => item.date),
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: window.innerWidth < 768 ? '10px' : '12px'
          },
          formatter: (value: string) => {
            if (!value) return '';

            try {
              if (selectedPeriod === 'day') {
                // للفترة اليومية، عرض الساعة بتنسيق 24 ساعة
                return value;
              } else if (selectedPeriod === 'week' || selectedPeriod === 'month') {
                // للفترات الأسبوعية والشهرية، عرض اليوم فقط
                if (value.includes('-')) {
                  const parts = value.split('-');
                  if (parts.length === 3) {
                    return parts[2]; // إرجاع اليوم فقط
                  }
                }
                return value;
              } else {
                // للفترة السنوية، عرض اختصار الشهر
                if (value.includes('-')) {
                  const parts = value.split('-');
                  if (parts.length === 2) {
                    const monthIndex = parseInt(parts[1]);
                    if (!isNaN(monthIndex) && monthIndex >= 1 && monthIndex <= 12) {
                      const months = ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'];
                      return months[monthIndex - 1];
                    }
                  }
                }
                return value;
              }
            } catch (error) {
              console.error('Error formatting x-axis label:', error, value);
              return value;
            }
          }
        },
        axisBorder: {
          show: true,
          color: isDark ? 'rgba(156, 163, 175, 0.4)' : 'rgba(107, 114, 128, 0.3)'
        },
        axisTicks: {
          show: true,
          color: isDark ? 'rgba(156, 163, 175, 0.4)' : 'rgba(107, 114, 128, 0.3)'
        },
        tickAmount: selectedPeriod === 'day' ? 8 : undefined
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: window.innerWidth < 768 ? '10px' : '12px'
          },
          formatter: (value: number) => {
            return value.toFixed(0);
          }
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        fontSize: '14px',
        fontFamily: 'Cairo, sans-serif',
        fontWeight: 600,
        labels: {
          colors: isDark ? '#E5E7EB' : '#374151',
          useSeriesColors: true // استخدام ألوان السلاسل
        },
        markers: {
          size: 10,
          strokeWidth: 3,
          fillColors: [
            isDark ? '#3B82F6' : '#1D4ED8', // لون المبيعات الفعلية
            isDark ? '#F87171' : '#DC2626'  // لون الفترة السابقة
          ],
          shape: 'circle',
          offsetX: 0,
          offsetY: 0
        },
        itemMargin: {
          horizontal: 20,
          vertical: 8
        },
        offsetY: 15
      },
      tooltip: {
        enabled: true,
        shared: true,
        intersect: false,
        theme: isDark ? 'dark' : 'light',
        fillSeriesColor: false,
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif'
        },
        custom: function({ series, dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= chartData.length) {
              return '';
            }

            // الحصول على البيانات لكلا السلسلتين
            const currentValue = series[0] ? series[0][dataPointIndex] : 0;
            const previousValue = series[1] ? series[1][dataPointIndex] : 0;
            const label = chartData[dataPointIndex].date;

            // تنسيق التاريخ حسب الفترة
            let formattedDate = '';
            if (selectedPeriod === 'day') {
              formattedDate = `الساعة ${label}`;
            } else if (selectedPeriod === 'week' || selectedPeriod === 'month') {
              if (label && label.includes('-')) {
                const parts = label.split('-');
                if (parts.length === 3) {
                  const year = parts[0];
                  const month = parseInt(parts[1]);
                  const day = parseInt(parts[2]);
                  if (!isNaN(month) && !isNaN(day) && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                    formattedDate = `${day} ${arabicMonths[month - 1]} ${year}`;
                  }
                }
              } else {
                formattedDate = label || '';
              }
            } else if (selectedPeriod === 'year') {
              if (label && label.includes('-')) {
                const parts = label.split('-');
                if (parts.length === 2) {
                  const year = parts[0];
                  const month = parseInt(parts[1]);
                  if (!isNaN(month) && month >= 1 && month <= 12) {
                    const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                    formattedDate = `${arabicMonths[month - 1]} ${year}`;
                  }
                }
              } else {
                formattedDate = label || '';
              }
            }

            // أسماء السلاسل
            const currentPeriodName = selectedPeriod === 'day' ? 'اليوم' :
                                    selectedPeriod === 'week' ? 'هذا الأسبوع' :
                                    selectedPeriod === 'month' ? 'هذا الشهر' : 'هذه السنة';

            const previousPeriodName = selectedPeriod === 'day' ? 'أمس' :
                                     selectedPeriod === 'week' ? 'الأسبوع السابق' :
                                     selectedPeriod === 'month' ? 'الشهر السابق' : 'السنة السابقة';

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 2px;
                padding: 16px 20px;
                box-shadow: 0 4px 15px ${isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)'},
                           0 2px 6px ${isDark ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.1)'};
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 200px;
                max-width: 260px;
                position: relative;
                overflow: hidden;
              ">
                <!-- Header with date -->
                <div style="
                  background: ${isDark ? 'rgba(59, 130, 246, 0.08)' : 'rgba(59, 130, 246, 0.04)'};
                  margin: -16px -20px 14px -20px;
                  padding: 10px 20px;
                  border-bottom: 1px solid ${isDark ? 'rgba(59, 130, 246, 0.12)' : 'rgba(59, 130, 246, 0.08)'};
                  border-radius: 2px 2px 0 0;
                ">
                  <div style="
                    color: ${isDark ? '#E2E8F0' : '#475569'};
                    font-size: 11px;
                    font-weight: 500;
                    text-align: center;
                    letter-spacing: 0.3px;
                  ">
                    ${formattedDate}
                  </div>
                </div>

                <!-- Data rows -->
                <div style="
                  display: flex;
                  flex-direction: column;
                  gap: 10px;
                ">
                  <!-- Current period -->
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 14px;
                    background: ${isDark ? 'rgba(59, 130, 246, 0.06)' : 'rgba(59, 130, 246, 0.03)'};
                    border-radius: 12px;
                    border: 1px solid ${isDark ? 'rgba(59, 130, 246, 0.12)' : 'rgba(59, 130, 246, 0.08)'};
                  ">
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: 8px;
                    ">
                      <div style="
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: ${isDark ? '#3B82F6' : '#2563EB'};
                        box-shadow: 0 0 0 2px ${isDark ? 'rgba(59, 130, 246, 0.3)' : 'rgba(37, 99, 235, 0.2)'};
                        flex-shrink: 0;
                      "></div>
                      <span style="
                        color: ${isDark ? '#F8FAFC' : '#1E293B'};
                        font-size: 12px;
                        font-weight: 600;
                      ">
                        ${currentPeriodName}
                      </span>
                    </div>
                    <div style="
                      color: ${isDark ? '#3B82F6' : '#2563EB'};
                      font-size: 13px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${currentValue.toLocaleString()} د.ل
                    </div>
                  </div>

                  <!-- Previous period -->
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 14px;
                    background: ${isDark ? 'rgba(248, 113, 113, 0.06)' : 'rgba(239, 68, 68, 0.03)'};
                    border-radius: 12px;
                    border: 1px solid ${isDark ? 'rgba(248, 113, 113, 0.12)' : 'rgba(239, 68, 68, 0.08)'};
                  ">
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: 8px;
                    ">
                      <div style="
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: ${isDark ? 'rgba(248, 113, 113, 0.8)' : 'rgba(239, 68, 68, 0.8)'};
                        box-shadow: 0 0 0 2px ${isDark ? 'rgba(248, 113, 113, 0.3)' : 'rgba(239, 68, 68, 0.2)'};
                        flex-shrink: 0;
                      "></div>
                      <span style="
                        color: ${isDark ? '#F8FAFC' : '#1E293B'};
                        font-size: 12px;
                        font-weight: 600;
                      ">
                        ${previousPeriodName}
                      </span>
                    </div>
                    <div style="
                      color: ${isDark ? 'rgba(248, 113, 113, 0.9)' : 'rgba(239, 68, 68, 0.9)'};
                      font-size: 13px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${previousValue.toLocaleString()} د.ل
                    </div>
                  </div>
                </div>

                ${currentValue > 0 && previousValue > 0 ? `
                  <!-- Comparison section -->
                  <div style="
                    margin-top: 14px;
                    padding: 10px 14px;
                    background: ${isDark ? 'rgba(156, 163, 175, 0.06)' : 'rgba(156, 163, 175, 0.03)'};
                    border-radius: 12px;
                    border: 1px solid ${isDark ? 'rgba(156, 163, 175, 0.12)' : 'rgba(156, 163, 175, 0.08)'};
                    text-align: center;
                  ">
                    <div style="
                      color: ${isDark ? '#9CA3AF' : '#6B7280'};
                      font-size: 10px;
                      font-weight: 500;
                      margin-bottom: 2px;
                    ">
                      نسبة التغيير
                    </div>
                    <div style="
                      color: ${((currentValue - previousValue) / previousValue * 100) >= 0 ?
                        (isDark ? '#10B981' : '#059669') :
                        (isDark ? '#F87171' : '#DC2626')};
                      font-size: 12px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${((currentValue - previousValue) / previousValue * 100) >= 0 ? '+' : ''}${((currentValue - previousValue) / previousValue * 100).toFixed(1)}%
                    </div>
                  </div>
                ` : ''}
              </div>
            `;
          } catch (error) {
            console.error('Error formatting tooltip:', error);
            return '';
          }
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 280
            },
            markers: {
              size: [5, 4], // أحجام مناسبة للجوال
              strokeWidth: [2, 2] // حدود متساوية للجوال
            },
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  colors: isDark ? '#9CA3AF' : '#6B7280'
                },
                rotate: 0,
                offsetY: 0
              }
            },
            yaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  colors: isDark ? '#9CA3AF' : '#6B7280'
                },
                formatter: (value: number) => {
                  // تبسيط الأرقام على الجوال
                  if (value >= 1000) {
                    return `${(value / 1000).toFixed(1)}K`;
                  }
                  return value.toFixed(0);
                }
              }
            }
          }
        }
      ]
    };

    // إعداد سلاسل البيانات المتعددة
    const series = [
      {
        name: `المبيعات ${selectedPeriod === 'day' ? 'اليوم' :
                        selectedPeriod === 'week' ? 'هذا الأسبوع' :
                        selectedPeriod === 'month' ? 'هذا الشهر' : 'هذه السنة'}`,
        type: chartType,
        data: chartData.map(item => {
          const amount = typeof item.amount === 'number' ? item.amount : 0;
          return Math.round(amount * 100) / 100;
        })
      },
      {
        name: `${selectedPeriod === 'day' ? 'أمس' :
                selectedPeriod === 'week' ? 'الأسبوع السابق' :
                selectedPeriod === 'month' ? 'الشهر السابق' : 'السنة السابقة'}`,
        type: 'line',
        data: previousPeriodData
      }
    ];

    return (
      <div className="touch-card">
        {/* Chart Container */}
        <div className={`h-96 md:h-80 ${isDark ? 'dark-chart' : 'light-chart'}`}>
          <ReactApexChart
            type={chartType as any}
            height="100%"
            width="100%"
            options={options}
            series={series}
          />
        </div>

        {/* Chart Summary - إحصائيات متقدمة */}
        <div className="mt-6 space-y-6">
          {/* الصف الأول - الإحصائيات الأساسية المهمة */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">أعلى قيمة</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">
                    {maxSale.toLocaleString()} <span className="text-lg font-medium text-gray-600 dark:text-gray-400">د.ل</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">أعلى مبيعات في الفترة</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaArrowUp className="text-lg text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">متوسط المبيعات</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">
                    {Math.round(avgSale).toLocaleString()} <span className="text-lg font-medium text-gray-600 dark:text-gray-400">د.ل</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">متوسط قيمة المبيعات</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaChartBar className="text-lg text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">إجمالي المبيعات</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">
                    {totalSales.toLocaleString()} <span className="text-lg font-medium text-gray-600 dark:text-gray-400">د.ل</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">مجموع مبيعات الفترة</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaShoppingCart className="text-lg text-indigo-600 dark:text-indigo-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">أقل قيمة</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">
                    {minSale > 0 ? minSale.toLocaleString() : '0'} <span className="text-lg font-medium text-gray-600 dark:text-gray-400">د.ل</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {minSale > 0 ? 'أدنى مبيعات في الفترة' : 'لا توجد مبيعات في الفترة'}
                  </p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaArrowDown className="text-lg text-orange-600 dark:text-orange-400" />
                </div>
              </div>
            </div>
          </div>

          {/* الصف الثاني - الإحصائيات المتقدمة الفريدة */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold uppercase tracking-wider mb-3">معدل النمو</p>
                  <div className="flex items-center gap-2 mb-2">
                    <p className={`text-2xl sm:text-3xl font-bold leading-tight ${growthRate >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {growthRate >= 0 ? '+' : ''}{growthRate.toFixed(1)}%
                    </p>
                    <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${growthRate >= 0 ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400' : 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'}`}>
                      {growthRate >= 0 ? (
                        <FaArrowUp className="text-xs mr-1" />
                      ) : (
                        <FaArrowDown className="text-xs mr-1" />
                      )}
                      <span>{growthRate >= 0 ? 'نمو' : 'انخفاض'}</span>
                    </div>
                  </div>
                  {isLoading ? (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      جاري تحميل البيانات...
                    </p>
                  ) : growthData.hasData ? (
                    <>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                        {selectedPeriod === 'day' ? 'مقارنة مع اليوم السابق' :
                         selectedPeriod === 'week' ? 'مقارنة مع الأسبوع السابق' :
                         selectedPeriod === 'month' ? 'مقارنة مع الشهر السابق' :
                         'مقارنة مع السنة السابقة'}
                      </p>

                      {/* القيم الحالية والسابقة في صف واحد */}
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 rounded-full bg-green-500 dark:bg-green-600 flex-shrink-0"></div>
                          <span className="text-gray-600 dark:text-gray-300 font-medium">الحالية:</span>
                          <span className="text-gray-900 dark:text-gray-100 font-bold">
                            {growthData.currentTotal.toLocaleString('ar-LY', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} د.ل
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 rounded-full bg-gray-400 dark:bg-gray-500 flex-shrink-0"></div>
                          <span className="text-gray-600 dark:text-gray-300 font-medium">السابقة:</span>
                          <span className="text-gray-900 dark:text-gray-100 font-bold">
                            {growthData.previousTotal.toLocaleString('ar-LY', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} د.ل
                          </span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      لا توجد بيانات للفترة السابقة للمقارنة
                    </p>
                  )}
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaChartLine className={`text-lg ${growthRate >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`} />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">الانحراف المعياري</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">{Math.round(standardDeviation).toLocaleString()}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">مقياس تشتت البيانات</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaSync className="text-lg text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider">عدد النقاط</p>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2">
                    {chartData.length} <span className="text-lg font-medium text-gray-600 dark:text-gray-400">نقطة</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">عدد نقاط البيانات</p>
                </div>
                <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <FaCalendarAlt className="text-lg text-purple-600 dark:text-purple-400" />
                </div>
              </div>
            </div>
          </div>

          {/* شريط التقدم للأداء */}
          <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <FaChartBar className="text-gray-600 dark:text-gray-300 text-sm" />
                </div>
                <span className="text-gray-900 dark:text-gray-100 font-bold text-lg">مؤشر الأداء</span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                {selectedPeriod === 'day' ? 'مقارنة بالساعات' :
                 selectedPeriod === 'week' ? 'مقارنة بالأيام' :
                 selectedPeriod === 'month' ? 'مقارنة بالأيام' : 'مقارنة بالأشهر'}
              </span>
            </div>

            <div className="w-full h-4 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden shadow-inner">
              <div
                className={`h-full rounded-full transition-all duration-1000 shadow-sm ${
                  avgSale > maxSale * 0.7 ? 'bg-gradient-to-r from-green-400 to-green-600' :
                  avgSale > maxSale * 0.4 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gradient-to-r from-red-400 to-red-600'
                }`}
                style={{ width: `${Math.min((avgSale / maxSale) * 100, 100)}%` }}
              ></div>
            </div>

            <div className="flex justify-between items-center text-xs font-medium mt-3">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span className="text-red-600 dark:text-red-400">ضعيف</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span className="text-yellow-600 dark:text-yellow-400">متوسط</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-green-600 dark:text-green-400">ممتاز</span>
              </div>
            </div>

            <div className="mt-3 text-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                النسبة الحالية: <span className="font-bold text-gray-900 dark:text-gray-100">
                  {Math.min((avgSale / maxSale) * 100, 100).toFixed(1)}%
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد مخطط فئات المنتجات باستخدام ApexCharts
  const renderProductsChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = productCategories.map(item => item.value);
    const chartLabels = productCategories.map(item => item.name);

    // إعداد خيارات المخطط
    const options: ApexOptions = {
      chart: {
        type: 'donut',
        fontFamily: 'Tajawal, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          speed: 500,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: isDark
        ? ['#34D399', '#FBBF24', '#F87171', '#60A5FA', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
        : ['#10B981', '#D97706', '#DC2626', '#2563EB', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'],
      labels: chartLabels,
      dataLabels: {
        enabled: true,
        formatter: (val: number) => {
          return `${val.toFixed(1)}%`;
        },
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif',
          fontWeight: '700',
          colors: ['#FFFFFF']
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          color: '#000000',
          opacity: 0.6
        }
      },
      stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'round',
        width: 3,
        colors: [isDark ? '#1F2937' : '#FFFFFF']
      },
      fill: {
        type: 'solid'
      },
      plotOptions: {
        pie: {
          donut: {
            size: '75%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '14px',
                fontFamily: 'Cairo, sans-serif',
                fontWeight: 600,
                color: isDark ? '#9CA3AF' : '#6B7280',
                offsetY: -25,
                formatter: function () {
                  return 'النسبة المئوية';
                }
              },
              value: {
                show: true,
                fontSize: '32px',
                fontFamily: 'Cairo, sans-serif',
                fontWeight: 800,
                color: isDark ? '#60A5FA' : '#3B82F6',
                offsetY: 20,
                formatter: function (val: any) {
                  return `${val}%`;
                }
              },
              total: {
                show: true,
                showAlways: true,
                label: 'توزيع الفئات',
                fontSize: '28px',
                fontFamily: 'Cairo, sans-serif',
                fontWeight: 800,
                color: isDark ? '#60A5FA' : '#2563EB',
                formatter: function () {
                  // عرض النسبة الكاملة مع نص توضيحي
                  return '100%';
                }
              }
            }
          },
          expandOnClick: true
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif'
        },
        custom: function({ series, seriesIndex, w }) {
          const categoryName = w.config.labels[seriesIndex];
          const percentage = series[seriesIndex];
          const categoryData = productCategories[seriesIndex];
          const count = categoryData?.count || 0;

          return `
            <div style="
              background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
              border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
              border-radius: 2px;
              padding: 12px 16px;
              box-shadow: 0 4px 15px ${isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)'},
                         0 2px 6px ${isDark ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.1)'};
              backdrop-filter: blur(10px);
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              min-width: 160px;
            ">
              <div style="
                color: ${isDark ? '#F3F4F6' : '#1F2937'};
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 6px;
              ">
                ${categoryName}
              </div>
              <div style="
                color: ${isDark ? '#E2E8F0' : '#475569'};
                font-size: 11px;
                margin-bottom: 4px;
              ">
                عدد المنتجات: ${count}
              </div>
              <div style="
                color: ${isDark ? '#60A5FA' : '#2563EB'};
                font-size: 14px;
                font-weight: 700;
              ">
                ${parseFloat(percentage).toFixed(1)}%
              </div>
            </div>
          `;
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom',
              fontSize: '12px'
            },
            dataLabels: {
              enabled: false
            }
          }
        }
      ]
    };

    // إعداد سلسلة البيانات
    const series = chartData;

    // حساب إحصائيات الفئات
    const totalCategories = productCategories.length;
    const totalProducts = productCategories.reduce((sum, item) => sum + (item.count || 0), 0);
    const largestCategory = productCategories.reduce((max, item) =>
      (item.count || 0) > (max.count || 0) ? item : max, productCategories[0] || { name: '', count: 0 });

    return (
      <div className="touch-card">
        {/* Chart Container */}
        <div className="h-80 md:h-96">
          <ReactApexChart
            type="donut"
            height="100%"
            width="100%"
            options={options}
            series={series}
          />
        </div>

        {/* Custom Legend */}
        <div className="mt-6 flex flex-wrap justify-center gap-4">
          {productCategories.map((category, index) => (
            <div key={index} className="flex items-center gap-3 px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
              <div
                className="w-4 h-4 rounded-full shadow-sm"
                style={{
                  backgroundColor: (isDark
                    ? ['#34D399', '#FBBF24', '#F87171', '#60A5FA', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
                    : ['#10B981', '#D97706', '#DC2626', '#2563EB', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'])[index % 8]
                }}
              ></div>
              <div className="text-center">
                <span className="text-sm font-semibold text-gray-900 dark:text-gray-100 block">
                  {category.name}
                </span>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {category.value.toFixed(1)}% • {category.count || 0} منتج
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Chart Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* إجمالي الفئات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي الفئات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalCategories}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaChartPie className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إجمالي المنتجات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المنتجات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalProducts.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaShoppingCart className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* أكبر فئة */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">أكبر فئة</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{largestCategory.name || 'لا توجد'}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">{largestCategory.count || 0} منتج</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaArrowUp className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>

          {/* منتجات غير نشطة */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">منتجات غير نشطة</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{inactiveProducts}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center">
                <FaChartBar className="text-danger-600 dark:text-danger-400 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد مخطط حالة المخزون باستخدام ApexCharts
  const renderInventoryChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = inventoryStatus.map(item => ({
      x: item.name,
      y: item.quantity
    }));

    // إعداد خيارات المخطط
    const options: ApexOptions = {
      chart: {
        type: 'bar',
        fontFamily: 'Cairo, sans-serif',
        toolbar: {
          show: false
        },
        zoom: {
          enabled: false
        },
        animations: {
          enabled: true,
          speed: 800
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: isDark ?
        ['#34D399', '#FBBF24', '#F87171'] :
        ['#10B981', '#D97706', '#DC2626'],
      dataLabels: {
        enabled: true,
        formatter: (val: number) => {
          return val > 0 ? val.toString() : '';
        },
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif',
          fontWeight: '700',
          colors: [isDark ? '#FFFFFF' : '#FFFFFF']
        },
        offsetY: -25,
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          color: isDark ? '#000000' : '#000000',
          opacity: 0.3
        }
      },
      grid: {
        borderColor: isDark ? '#374151' : '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      xaxis: {
        type: 'category',
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px'
          }
        }
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif'
        },
        custom: function({ series, dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= inventoryStatus.length) {
              return '';
            }

            const statusData = inventoryStatus[dataPointIndex];
            const quantity = series[0][dataPointIndex];
            const statusName = statusData.name;
            const totalQuantity = statusData.total_quantity || 0;
            const value = statusData.value || 0;

            // تحديد اللون حسب الحالة
            let statusColor = '';
            if (statusName === 'متوفر') {
              statusColor = isDark ? '#34D399' : '#10B981';
            } else if (statusName === 'منخفض') {
              statusColor = isDark ? '#FBBF24' : '#D97706';
            } else if (statusName === 'نفذ') {
              statusColor = isDark ? '#F87171' : '#DC2626';
            }

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 2px;
                padding: 12px 16px;
                box-shadow: 0 4px 15px ${isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)'},
                           0 2px 6px ${isDark ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.1)'};
                backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 180px;
              ">
                <div style="
                  color: ${isDark ? '#F3F4F6' : '#1F2937'};
                  font-size: 14px;
                  font-weight: 600;
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                ">
                  <div style="
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: ${statusColor};
                    margin-left: 8px;
                  "></div>
                  ${statusName}
                </div>
                <div style="
                  color: ${isDark ? '#E2E8F0' : '#475569'};
                  font-size: 11px;
                  margin-bottom: 4px;
                ">
                  عدد المنتجات: ${quantity}
                </div>
                <div style="
                  color: ${isDark ? '#E2E8F0' : '#475569'};
                  font-size: 11px;
                  margin-bottom: 4px;
                ">
                  إجمالي الكمية: ${totalQuantity.toLocaleString()}
                </div>
                <div style="
                  color: ${statusColor};
                  font-size: 13px;
                  font-weight: 700;
                ">
                  القيمة: ${value.toLocaleString()} د.ل
                </div>
              </div>
            `;
          } catch (error) {
            return '';
          }
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 8,
          columnWidth: '65%',
          dataLabels: {
            position: 'top'
          },
          distributed: true
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '12px',
        fontFamily: 'Cairo, sans-serif',
        fontWeight: 500,
        labels: {
          colors: isDark ? '#E5E7EB' : '#374151'
        },
        markers: {
          size: 8,
          shape: 'square'
        },
        itemMargin: {
          horizontal: 15,
          vertical: 5
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 280
            },
            plotOptions: {
              bar: {
                columnWidth: '80%'
              }
            },
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px'
                }
              }
            }
          }
        }
      ]
    };

    // إعداد سلسلة البيانات
    const series = [{
      name: 'الكمية',
      data: chartData
    }];

    // حساب إحصائيات المخزون
    const totalQuantity = chartData.reduce((sum, item) => sum + (typeof item.y === 'number' ? item.y : 0), 0);
    const maxQuantity = Math.max(...chartData.map(item => typeof item.y === 'number' ? item.y : 0));
    const avgQuantity = chartData.length > 0 ? totalQuantity / chartData.length : 0;

    return (
      <div className="touch-card">
        {/* Chart Container */}
        <div className="h-80 md:h-64">
          <ReactApexChart
            type="bar"
            height="100%"
            width="100%"
            options={options}
            series={series}
          />
        </div>

        {/* Chart Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* أعلى كمية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">أعلى كمية</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{maxQuantity.toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaArrowUp className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* متوسط الكمية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">متوسط الكمية</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{Math.round(avgQuantity).toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaChartBar className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إجمالي المخزون */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المخزون</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalQuantity.toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaHdd className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد عرض إحصائيات النظام
  const renderSystemStats = () => {
    return (
      <div>
        {/* عنوان القسم */}
        <div className="flex items-center mb-6">
          <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
            <FaServer className="text-xl" />
          </div>
          <h2 className="font-bold text-xl text-secondary-900 dark:text-secondary-100">معلومات النظام</h2>
        </div>

        {/* System Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {/* إجمالي المستخدمين */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.totalUsers}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaUsers className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* المستخدمين النشطين */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">المستخدمين النشطين</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.activeUsers}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">آخر 30 يوم</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaUserCheck className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* نسبة النشاط */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">نسبة النشاط</p>
                <p className="text-2xl font-bold text-success-600 dark:text-success-400">
                  {systemStats.totalUsers > 0
                    ? Math.round((systemStats.activeUsers / systemStats.totalUsers) * 100)
                    : 0}%
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-info-100 dark:bg-info-900/30 flex items-center justify-center">
                <FaArrowUp className="text-info-600 dark:text-info-400 text-xl" />
              </div>
            </div>
          </div>

          {/* آخر تسجيل دخول */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">آخر تسجيل دخول</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{systemStats.lastLogin}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaUserCheck className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>

          {/* وقت تشغيل النظام */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">وقت تشغيل النظام</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.systemUptime}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <FaClock className="text-purple-600 dark:text-purple-400 text-xl" />
              </div>
            </div>
          </div>

          {/* حجم قاعدة البيانات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">حجم قاعدة البيانات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.databaseSize}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-secondary-100 dark:bg-secondary-900/30 flex items-center justify-center">
                <FaHdd className="text-secondary-600 dark:text-secondary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* آخر نسخة احتياطية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">آخر نسخة احتياطية</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{systemStats.lastBackup}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center">
                <FaDatabase className="text-danger-600 dark:text-danger-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إصدار النظام */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إصدار النظام</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">SmartPOS v1.0.0</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">React + FastAPI</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <FaCog className="text-blue-600 dark:text-blue-400 text-xl" />
              </div>
            </div>
          </div>

          {/* حالة الخادم */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">حالة الخادم</p>
                <p className="text-lg font-bold text-success-600 dark:text-success-400">متصل</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">localhost:8002</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaServer className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>
        </div>

        {/* إجراءات النظام وجدول النسخ الاحتياطية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* قسم الإجراءات */}
          <div className="touch-card">
            <div className="flex justify-between items-center mb-5">
              <div className="flex items-center">
                <div className="bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300 p-2 rounded-lg ml-3">
                  <FaCog className="text-xl" />
                </div>
                <h3 className="font-bold text-lg text-secondary-900 dark:text-secondary-100">إجراءات النظام</h3>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mb-6">
              <button
                onClick={openCreateBackupModal}
                className="btn-primary flex items-center"
                disabled={isLoading}
              >
                <FaDatabase className="ml-2" />
                <span>إنشاء نسخة احتياطية</span>
              </button>
              <button
                onClick={openSystemUpdateModal}
                className="btn-secondary flex items-center"
                disabled={isLoading}
              >
                <FaSync className="ml-2" />
                <span>تحديث النظام</span>
              </button>
              <button
                onClick={openClearCacheModal}
                className="btn-outline flex items-center"
                disabled={isLoading}
              >
                <FaTrash className="ml-2" />
                <span>مسح التخزين المؤقت</span>
              </button>
            </div>

            {/* مكون تشخيص التوثيق */}
            <AuthDebug />
          </div>

          {/* جدول النسخ الاحتياطية */}
          <div className="touch-card">
            <div className="flex justify-between items-center mb-5">
              <div className="flex items-center">
                <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
                  <FaDatabase className="text-xl" />
                </div>
                <h3 className="font-bold text-lg text-secondary-900 dark:text-secondary-100">النسخ الاحتياطية</h3>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setAllBackupsModal(true)}
                  className="btn-outline-sm flex items-center"
                >
                  <FaDatabase className="ml-2" />
                  <span>عرض الكل</span>
                </button>
                <button
                  onClick={fetchBackups}
                  className="btn-outline-sm flex items-center"
                  disabled={backupsLoading}
                >
                  <FaSync className={`ml-2 ${backupsLoading ? 'animate-spin' : ''}`} />
                  <span>تحديث</span>
                </button>
              </div>
            </div>

            {backupsLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-secondary-600 dark:text-secondary-300">جاري التحميل...</p>
              </div>
            ) : backups.length === 0 ? (
              <div className="text-center py-8">
                <FaDatabase className="mx-auto h-12 w-12 text-secondary-400 dark:text-secondary-500 mb-3" />
                <p className="text-secondary-600 dark:text-secondary-300">لا توجد نسخ احتياطية</p>
                <p className="text-sm text-secondary-500 dark:text-secondary-400 mt-1">
                  قم بإنشاء نسخة احتياطية أولاً
                </p>
              </div>
            ) : (
              <div>
                {/* عرض الجدول للشاشات الكبيرة */}
                <div className="hidden md:block overflow-x-auto custom-scrollbar">
                  <table className="min-w-full divide-y divide-secondary-200 dark:divide-secondary-700">
                    <thead className="bg-secondary-50 dark:bg-secondary-800">
                      <tr>
                        <th className="px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider">
                          اسم الملف
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider">
                          الحجم
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider">
                          التاريخ
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-secondary-800 divide-y divide-secondary-200 dark:divide-secondary-700">
                      {backups.slice(0, 4).map((backup, index) => (
                        <tr key={backup.name} className={index % 2 === 0 ? 'bg-white dark:bg-secondary-800' : 'bg-secondary-50 dark:bg-secondary-700/50'}>
                          <td className="px-4 py-3 text-sm font-medium text-secondary-900 dark:text-secondary-100">
                            <div className="truncate max-w-xs" title={backup.name}>
                              {backup.name}
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-secondary-600 dark:text-secondary-300">
                            {backup.size}
                          </td>
                          <td className="px-4 py-3 text-sm text-secondary-600 dark:text-secondary-300">
                            <div>
                              <div>{backup.created_date}</div>
                              <div className="text-xs text-secondary-500 dark:text-secondary-400">{backup.created_time}</div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm font-medium">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={() => openRestoreModal(backup.name)}
                                className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-1 rounded"
                                title="استعادة النسخة الاحتياطية"
                              >
                                <FaUndo className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => openDeleteModal(backup.name)}
                                className="text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-1 rounded"
                                title="حذف النسخة الاحتياطية"
                              >
                                <FaTrash className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* عرض البطاقات للهواتف والأجهزة اللوحية */}
                <div className="md:hidden space-y-3">
                  {backups.slice(0, 4).map((backup, index) => (
                    <div
                      key={backup.name}
                      className="bg-white dark:bg-secondary-800 border border-secondary-200 dark:border-secondary-700 rounded-lg p-4 shadow-sm"
                    >
                      {/* رأس البطاقة */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-2">
                            <FaDatabase className="text-primary-600 dark:text-primary-400 ml-2 flex-shrink-0" />
                            <h4 className="text-sm font-medium text-secondary-900 dark:text-secondary-100 truncate">
                              نسخة احتياطية #{index + 1}
                            </h4>
                          </div>
                          <p className="text-xs text-secondary-600 dark:text-secondary-300 break-all">
                            {backup.name}
                          </p>
                        </div>

                        {/* أزرار الإجراءات */}
                        <div className="flex items-center space-x-1 space-x-reverse ml-3">
                          <button
                            onClick={() => openRestoreModal(backup.name)}
                            className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20"
                            title="استعادة النسخة الاحتياطية"
                          >
                            <FaUndo className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => openDeleteModal(backup.name)}
                            className="text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-2 rounded-lg hover:bg-danger-50 dark:hover:bg-danger-900/20"
                            title="حذف النسخة الاحتياطية"
                          >
                            <FaTrash className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      {/* معلومات البطاقة */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-secondary-500 dark:text-secondary-400 text-xs">الحجم</span>
                          <p className="font-medium text-secondary-900 dark:text-secondary-100">{backup.size}</p>
                        </div>
                        <div>
                          <span className="text-secondary-500 dark:text-secondary-400 text-xs">التاريخ</span>
                          <p className="font-medium text-secondary-900 dark:text-secondary-100">{backup.created_date}</p>
                          <p className="text-xs text-secondary-500 dark:text-secondary-400">{backup.created_time}</p>
                        </div>
                      </div>

                      {/* علامة أحدث نسخة */}
                      {index === 0 && (
                        <div className="mt-3 pt-3 border-t border-secondary-200 dark:border-secondary-700">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300">
                            أحدث نسخة احتياطية
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* رسالة عرض المزيد */}
                {backups.length > 4 && (
                  <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 mt-4 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      يتم عرض 4 نسخ من أصل {backups.length} نسخة احتياطية.{' '}
                      <button
                        onClick={() => setAllBackupsModal(true)}
                        className="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium"
                      >
                        عرض جميع النسخ
                      </button>
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // الحصول على التاريخ الحالي
  const currentDate = getCurrentTripoliDateTime();
  const formattedDate = currentDate.toLocaleDateString('ar-LY', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // تحديث البيانات
  const handleRefresh = () => {
    if (selectedReportType === 'sales') {
      fetchSalesTrends(selectedPeriod);
    } else if (selectedReportType === 'products') {
      fetchProductCategories();
    } else if (selectedReportType === 'inventory') {
      fetchInventoryStatus();
    } else if (selectedReportType === 'system') {
      fetchSystemStats();
    }
  };

  // فتح نافذة تأكيد إنشاء النسخة الاحتياطية
  const openCreateBackupModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'backup',
      title: 'تأكيد إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات الحالية؟',
      description: 'سيتم إنشاء نسخة كاملة من جميع البيانات وحفظها في مجلد النسخ الاحتياطية.',
      isLoading: false
    });
  };

  // تأكيد إنشاء النسخة الاحتياطية
  const confirmCreateBackup = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch('/api/dashboard/create-backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // إغلاق نافذة التأكيد
        setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

        // عرض نافذة النجاح
        setSuccessModal({
          isOpen: true,
          title: 'تم الإنشاء بنجاح',
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
          details: {
            backup_name: data.backup_name,
            size: data.size,
            created_at: data.created_at
          },
          autoClose: true
        });

        // تحديث إحصائيات النظام وقائمة النسخ الاحتياطية
        fetchSystemStats();
        fetchBackups();
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error creating backup:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الإنشاء',
        message: `فشل في إنشاء النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد تحديث النظام
  const openSystemUpdateModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'update',
      title: 'تأكيد تحديث النظام',
      message: 'هل تريد تحديث النظام إلى أحدث إصدار؟',
      description: 'سيتم فحص التحديثات المتاحة وتطبيقها على النظام.',
      isLoading: false
    });
  };

  // تأكيد تحديث النظام
  const confirmSystemUpdate = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      // محاكاة عملية التحديث
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إغلاق نافذة التأكيد
      setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

      // عرض نافذة النجاح
      setSuccessModal({
        isOpen: true,
        title: 'تم التحديث بنجاح',
        message: 'تم تحديث النظام بنجاح',
        details: {
          backup_name: 'تحديث النظام',
          size: 'مكتمل',
          created_at: new Date().toLocaleString('ar-LY')
        },
        autoClose: true
      });
    } catch (error) {
      console.error('Error updating system:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في التحديث',
        message: 'فشل في تحديث النظام',
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد مسح التخزين المؤقت
  const openClearCacheModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'clear-cache',
      title: 'تأكيد مسح التخزين المؤقت',
      message: 'هل تريد مسح جميع بيانات التخزين المؤقت؟',
      description: 'سيتم مسح جميع البيانات المحفوظة محلياً وإعادة تحميل الصفحة.',
      isLoading: false
    });
  };

  // تأكيد مسح التخزين المؤقت
  const confirmClearCache = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      // محاكاة عملية المسح
      await new Promise(resolve => setTimeout(resolve, 1000));

      // مسح localStorage
      localStorage.clear();
      // مسح sessionStorage
      sessionStorage.clear();

      // إغلاق نافذة التأكيد
      setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

      // عرض نافذة النجاح مع إعادة تحميل
      setSuccessModal({
        isOpen: true,
        title: 'تم المسح بنجاح',
        message: 'تم مسح التخزين المؤقت بنجاح',
        details: {
          backup_name: 'مسح التخزين المؤقت',
          size: 'مكتمل',
          created_at: new Date().toLocaleString('ar-LY')
        },
        autoClose: true
      });

      // إعادة تحميل الصفحة بعد ثانيتين
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error clearing cache:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في المسح',
        message: 'فشل في مسح التخزين المؤقت',
        details: null,
        autoClose: false
      });
    }
  };

  // وظيفة موحدة للتعامل مع تأكيد النوافذ
  const handleConfirmAction = () => {
    switch (confirmModal.type) {
      case 'backup':
        confirmCreateBackup();
        break;
      case 'update':
        confirmSystemUpdate();
        break;
      case 'clear-cache':
        confirmClearCache();
        break;
      default:
        break;
    }
  };

  // دالة عرض تقرير مبيعات المستخدمين اليومية
  const renderDailyUserSales = () => {


    // التحقق من صلاحيات المدير
    if (!currentUser || currentUser.role !== 'admin') {

      return (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4 rounded-full mb-3 inline-block">
            <FaCog className="text-3xl" />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">غير مصرح</h3>
          <p className="text-gray-600 dark:text-gray-400">هذا التقرير متاح للمديرين فقط</p>
        </div>
      );
    }

    if (dailyUserSalesLoading) {
      return (
        <div className="flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
        </div>
      );
    }

    if (!dailyUserSales) {
      return (
        <div className="space-y-6">
          {/* شريط التحكم */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
            <div className="flex items-center gap-3">
              <FaCalendarDay className="text-primary-600 dark:text-primary-400" />
              <span className="font-medium text-gray-700 dark:text-gray-300">تاريخ التقرير:</span>
              <div className="w-48">
                <DatePicker
                  name="reportDate"
                  value={selectedDate}
                  onChange={(date) => {
                    setSelectedDate(date);
                    fetchDailyUserSales(date);
                  }}
                  placeholder="اختر التاريخ"
                  className="text-sm"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                آخر تحديث: {lastUpdateTime.toLocaleString('ar-LY')}
              </div>
              <button
                onClick={() => fetchDailyUserSales(selectedDate, true)}
                disabled={dailyUserSalesLoading}
                className="flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed"
                title="تحديث البيانات"
              >
                <FaSync className={`text-xs ${dailyUserSalesLoading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>

          {/* رسالة لا توجد بيانات */}
          <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 p-4 rounded-full mb-3 inline-block">
              <FaUsers className="text-3xl" />
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">لا توجد بيانات مبيعات</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              لا توجد بيانات مبيعات للتاريخ المحدد: {selectedDate}
            </p>
            <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <p>• تأكد من أن التاريخ المحدد صحيح</p>
              <p>• تأكد من وجود مبيعات في هذا التاريخ</p>
              <p>• جرب تاريخ آخر أو اليوم الحالي</p>
            </div>
          </div>
        </div>
      );
    }

    // إعداد بيانات المخطط مع ألوان مختلفة لكل مستخدم
    const chartData = dailyUserSales.top_users.map((user: any, index: number) => ({
      name: user.full_name || user.username,
      value: parseFloat(user.total_received) || 0,
      sales_count: parseInt(user.total_sales) || 0,
      products_total: parseFloat(user.products_total) || 0,
      discounts: parseFloat(user.total_discounts) || 0,
      taxes: parseFloat(user.total_taxes) || 0,
      debts: parseFloat(user.total_debts) || 0,
      color: index
    }));

    // ألوان متدرجة للمستخدمين حسب الأداء
    const userColors = isDark
      ? ['#60A5FA', '#34D399', '#FBBF24', '#F87171', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
      : ['#2563EB', '#059669', '#D97706', '#DC2626', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'];



    const chartOptions: ApexOptions = {
      chart: {
        type: 'bar',
        fontFamily: 'Cairo, sans-serif',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        animations: {
          enabled: true,
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          }
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: userColors,
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 8,
          columnWidth: '75%',
          dataLabels: {
            position: 'top'
          },
          distributed: true
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: chartData.map((item: any) => item.name),
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px',
            fontFamily: 'Cairo, sans-serif'
          },
          formatter: function (val: any) {
            return val.length > 15 ? val.substring(0, 15) + '...' : val;
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px',
            fontFamily: 'Cairo, sans-serif'
          },
          formatter: function (val: any) {
            const numVal = parseFloat(val);
            return isNaN(numVal) ? '' : `${numVal.toFixed(0)} د.ل`;
          }
        }
      },
      grid: {
        borderColor: isDark ? '#374151' : '#E5E7EB',
        strokeDashArray: 3,
        yaxis: {
          lines: {
            show: true
          }
        },
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'Cairo, sans-serif'
        },
        custom: function({ dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= chartData.length) {
              return '';
            }

            const userData = chartData[dataPointIndex];
            const userColor = userColors[dataPointIndex % userColors.length];

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 2px;
                padding: 16px 20px;
                box-shadow: 0 4px 15px ${isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)'},
                           0 2px 6px ${isDark ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.1)'};
                backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 220px;
              ">
                <div style="
                  color: ${isDark ? '#F3F4F6' : '#1F2937'};
                  font-size: 15px;
                  font-weight: 700;
                  margin-bottom: 12px;
                  display: flex;
                  align-items: center;
                ">
                  <div style="
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: ${userColor};
                    margin-left: 8px;
                  "></div>
                  ${userData.name}
                </div>
                <div style="
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 8px;
                  margin-bottom: 8px;
                ">
                  <div style="
                    color: ${isDark ? '#E2E8F0' : '#475569'};
                    font-size: 11px;
                  ">
                    عدد المبيعات: <span style="font-weight: 600;">${userData.sales_count}</span>
                  </div>
                  <div style="
                    color: ${isDark ? '#E2E8F0' : '#475569'};
                    font-size: 11px;
                  ">
                    سعر المنتجات: <span style="font-weight: 600;">${userData.products_total.toFixed(0)} د.ل</span>
                  </div>
                  <div style="
                    color: ${isDark ? '#FBBF24' : '#D97706'};
                    font-size: 11px;
                  ">
                    الخصومات: <span style="font-weight: 600;">-${userData.discounts.toFixed(0)} د.ل</span>
                  </div>
                  <div style="
                    color: ${isDark ? '#60A5FA' : '#2563EB'};
                    font-size: 11px;
                  ">
                    الضرائب: <span style="font-weight: 600;">+${userData.taxes.toFixed(0)} د.ل</span>
                  </div>
                </div>
                <div style="
                  border-top: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.5)' : 'rgba(229, 231, 235, 0.5)'};
                  padding-top: 8px;
                  margin-top: 8px;
                ">
                  <div style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    margin-bottom: 8px;
                  ">
                    <div style="
                      color: ${isDark ? '#10B981' : '#059669'};
                      font-size: 12px;
                      font-weight: 700;
                      text-align: center;
                    ">
                      المبلغ المستلم: ${userData.value.toFixed(0)} د.ل
                    </div>
                    <div style="
                      color: ${isDark ? '#F87171' : '#DC2626'};
                      font-size: 12px;
                      font-weight: 700;
                      text-align: center;
                    ">
                      الديون: ${userData.debts.toFixed(0)} د.ل
                    </div>
                  </div>
                  <div style="
                    color: ${userColor};
                    font-size: 14px;
                    font-weight: 800;
                    text-align: center;
                    border-top: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.3)'};
                    padding-top: 6px;
                  ">
                    الإجمالي: ${(userData.value + userData.debts).toFixed(0)} د.ل
                  </div>
                </div>
              </div>
            `;
          } catch (error) {
            return '';
          }
        }
      },
      legend: {
        show: false
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              show: false
            }
          }
        }
      ]
    };

    const series = [{
      name: 'المبلغ المستلم',
      data: chartData.map((item: any) => parseFloat(item.value) || 0)
    }];

    return (
      <div className="space-y-6">
        {/* شريط التحكم */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <FaCalendarDay className="text-primary-600 dark:text-primary-400" />
            <span className="font-medium text-gray-700 dark:text-gray-300">تاريخ التقرير:</span>
            <div className="w-48">
              <DatePicker
                name="reportDate"
                value={selectedDate}
                onChange={(date) => {
                  setSelectedDate(date);
                  fetchDailyUserSales(date);
                }}
                placeholder="اختر التاريخ"
                className="text-sm"
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              آخر تحديث: {lastUpdateTime.toLocaleString('ar-LY')}
            </div>
            <button
              onClick={() => fetchDailyUserSales(selectedDate, true)}
              disabled={dailyUserSalesLoading}
              className="flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed"
              title="تحديث البيانات"
            >
              <FaSync className={`text-xs ${dailyUserSalesLoading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
          </div>
        </div>

        {/* ملخص الإحصائيات */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">عدد المبيعات</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">{dailyUserSales.summary.total_sales_count}</p>
              </div>
              <FaShoppingCart className="text-blue-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">سعر المنتجات</p>
                <p className="text-lg font-bold text-gray-600 dark:text-gray-400">{dailyUserSales.summary.total_products_amount.toFixed(2)} د.ل</p>
              </div>
              <FaShoppingCart className="text-gray-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">إجمالي الخصومات</p>
                <p className="text-lg font-bold text-orange-600 dark:text-orange-400">-{dailyUserSales.summary.total_discounts_amount.toFixed(2)} د.ل</p>
              </div>
              <FaArrowDown className="text-orange-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">إجمالي الضرائب</p>
                <p className="text-lg font-bold text-blue-600 dark:text-blue-400">+{dailyUserSales.summary.total_taxes_amount.toFixed(2)} د.ل</p>
              </div>
              <FaArrowUp className="text-blue-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">المبلغ المستلم</p>
                <p className="text-lg font-bold text-success-600 dark:text-success-400">{dailyUserSales.summary.total_received_amount.toFixed(2)} د.ل</p>
              </div>
              <FaArrowUp className="text-success-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">الديون اليوم</p>
                <p className="text-lg font-bold text-danger-600 dark:text-danger-400">{dailyUserSales.summary.total_debts_amount?.toFixed(2) || '0.00'} د.ل</p>
              </div>
              <FaExclamationTriangle className="text-danger-500 text-xl" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">المستخدمين النشطين</p>
                <p className="text-lg font-bold text-primary-600 dark:text-primary-400">{dailyUserSales.summary.active_users_count}</p>
              </div>
              <FaUserCheck className="text-primary-500 text-xl" />
            </div>
          </div>
        </div>

        {/* مخطط أفضل المستخدمين */}
        {chartData.length > 0 && (
          <div className="touch-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <FaChartBar className="ml-2 text-primary-600 dark:text-primary-400" />
                أفضل المستخدمين مبيعاً
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                أعلى {chartData.length} مستخدمين
              </div>
            </div>

            {/* Chart Container */}
            <div className="h-80 md:h-96">
              <ReactApexChart
                type="bar"
                height="100%"
                width="100%"
                options={chartOptions}
                series={series}
              />
            </div>

            {/* Chart Legend - عرض القيم أسفل المخطط */}
            <div className="mt-4 flex flex-wrap justify-center gap-4">
              {chartData.map((item: any, index: number) => (
                <div key={index} className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div
                    className="w-3 h-3 rounded-sm"
                    style={{ backgroundColor: userColors[index % userColors.length] }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.name}: <span className="font-bold text-gray-900 dark:text-gray-100">{item.value.toFixed(0)} د.ل</span>
                  </span>
                </div>
              ))}
            </div>

            {/* Chart Summary */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* أفضل مستخدم */}
              {chartData[0] && (
                <div className="touch-card p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">أفضل مستخدم</p>
                      <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{chartData[0].name}</p>
                      <p className="text-xs text-secondary-500 dark:text-secondary-400">{chartData[0].sales_count} مبيعة</p>
                    </div>
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: userColors[0] + '20' }}>
                      <FaUserCheck className="text-xl" style={{ color: userColors[0] }} />
                    </div>
                  </div>
                </div>
              )}

              {/* إجمالي المبيعات */}
              <div className="touch-card p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المبيعات</p>
                    <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">
                      {chartData.reduce((sum: number, user: any) => sum + user.sales_count, 0)}
                    </p>
                    <p className="text-xs text-secondary-500 dark:text-secondary-400">من {chartData.length} مستخدمين</p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                    <FaShoppingCart className="text-success-600 dark:text-success-400 text-xl" />
                  </div>
                </div>
              </div>

              {/* إجمالي المبلغ */}
              <div className="touch-card p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المبلغ</p>
                    <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">
                      {chartData.reduce((sum: number, user: any) => sum + user.value, 0).toFixed(2)} د.ل
                    </p>
                    <p className="text-xs text-secondary-500 dark:text-secondary-400">المبلغ المستلم</p>
                  </div>
                  <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                    <FaArrowUp className="text-primary-600 dark:text-primary-400 text-xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* جدول تفاصيل المستخدمين */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <FaUsers className="ml-2 text-primary-600 dark:text-primary-400" />
              تفاصيل مبيعات المستخدمين
            </h3>
          </div>
          <div className="overflow-x-auto custom-scrollbar">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    عدد المبيعات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    سعر المنتجات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الخصومات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الضرائب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المستلم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الديون
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {dailyUserSales.users.map((user: any) => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                          {user.full_name ? user.full_name.charAt(0) : user.username.charAt(0)}
                        </div>
                        <div className="mr-3">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.full_name || user.username}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            @{user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {user.total_sales}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {user.products_total.toFixed(2)} د.ل
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">
                      -{user.total_discounts.toFixed(2)} د.ل
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">
                      +{user.total_taxes.toFixed(2)} د.ل
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-success-600 dark:text-success-400">
                      {user.total_received.toFixed(2)} د.ل
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-danger-600 dark:text-danger-400">
                      {user.total_debts?.toFixed(2) || '0.00'} د.ل
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="touch-container">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaChartLine className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">التقارير</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  تقارير وإحصائيات شاملة للنظام
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="تحديث البيانات"
              >
                <FaSync className="text-sm" />
              </button>
              <button
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="طباعة التقرير"
              >
                <FaPrint className="text-sm" />
              </button>
              <button
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="تصدير التقرير"
              >
                <FaDownload className="text-sm" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Date and User Info Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6">
        <div className="text-secondary-600 dark:text-secondary-300 text-sm text-center">
          <span className="font-medium">{formattedDate}</span>
          <span className="mx-2">|</span>
          <span>{currentUser?.role === 'admin' ? 'جميع المستخدمين' : 'مبيعاتك فقط'}</span>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="touch-card bg-white dark:bg-gray-800 shadow-soft rounded-xl p-4 mb-6">
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
          <button
            onClick={() => handleReportTypeChange('sales')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'sales'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartLine className="ml-2" />
            <span>المبيعات</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('products')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'products'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartPie className="ml-2" />
            <span>المنتجات</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('inventory')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'inventory'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartBar className="ml-2" />
            <span>المخزون</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('daily-users')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'daily-users'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaUsers className="ml-2" />
            <span>مبيعات المستخدمين</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('system')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'system'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaServer className="ml-2" />
            <span>النظام</span>
          </button>
        </div>

        {selectedReportType === 'sales' && (
          <div className="flex space-x-2 space-x-reverse mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
            <button
              onClick={() => handlePeriodChange('day')}
              className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                selectedPeriod === 'day'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaCalendarDay className="ml-1.5" />
              <span>اليوم</span>
            </button>
            <button
              onClick={() => handlePeriodChange('week')}
              className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                selectedPeriod === 'week'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaCalendarWeek className="ml-1.5" />
              <span>الأسبوع</span>
            </button>
            <button
              onClick={() => handlePeriodChange('month')}
              className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                selectedPeriod === 'month'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaCalendarAlt className="ml-1.5" />
              <span>الشهر</span>
            </button>
            <button
              onClick={() => handlePeriodChange('year')}
              className={`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                selectedPeriod === 'year'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaCalendar className="ml-1.5" />
              <span>السنة</span>
            </button>
          </div>
        )}



        {isLoading ? (
          <div className="flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
          </div>
        ) : (
          <>
            {selectedReportType === 'sales' && renderSalesChart()}
            {selectedReportType === 'products' && renderProductsChart()}
            {selectedReportType === 'inventory' && renderInventoryChart()}
            {selectedReportType === 'daily-users' && renderDailyUserSales()}
            {selectedReportType === 'system' && renderSystemStats()}
            {selectedReportType === 'customers' && (
              <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                <div className="bg-secondary-100 dark:bg-secondary-900/50 text-secondary-700 dark:text-secondary-300 p-4 rounded-full mb-3 inline-block">
                  <FaChartBar className="text-3xl" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-secondary-900 dark:text-secondary-100">تقرير العملاء قيد التطوير</h3>
                <p className="text-secondary-600 dark:text-secondary-400">سيتم إضافة هذه الميزة قريباً</p>
              </div>
            )}
          </>
        )}
      </div>

      {/* النوافذ المخصصة */}

      {/* نافذة تأكيد الحذف */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, backupName: '', isLoading: false })}
        onConfirm={confirmDeleteBackup}
        title="تأكيد حذف النسخة الاحتياطية"
        message="هل أنت متأكد من حذف النسخة الاحتياطية التالية؟"
        itemName={deleteModal.backupName}
        isLoading={deleteModal.isLoading}
      />

      {/* نافذة تأكيد الاستعادة */}
      <RestoreConfirmModal
        isOpen={restoreModal.isOpen}
        onClose={() => setRestoreModal({ isOpen: false, backupInfo: null, isLoading: false })}
        onConfirm={confirmRestoreBackup}
        backupInfo={restoreModal.backupInfo}
        isLoading={restoreModal.isLoading}
      />

      {/* نافذة النجاح */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, title: '', message: '', details: null, autoClose: false })}
        title={successModal.title}
        message={successModal.message}
        details={successModal.details}
        autoClose={successModal.autoClose}
        autoCloseDelay={3000}
      />

      {/* نافذة التأكيد العامة */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={() => setConfirmModal({ isOpen: false, type: 'backup', title: '', message: '', description: '', isLoading: false })}
        onConfirm={handleConfirmAction}
        title={confirmModal.title}
        message={confirmModal.message}
        description={confirmModal.description}
        type={confirmModal.type}
        isLoading={confirmModal.isLoading}
      />

      {/* نافذة عرض جميع النسخ الاحتياطية */}
      <AllBackupsModal
        isOpen={allBackupsModal}
        onClose={() => setAllBackupsModal(false)}
        onRestore={openRestoreModal}
        onDelete={openDeleteModal}
      />

    </div>
  );
};

export default Reports;
