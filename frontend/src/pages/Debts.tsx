import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  FaPlus,
  FaMoneyBillWave,
  FaUser,
  FaCalendarAlt,
  FaCheck,
  FaTimes,
  FaEdit,
  FaTrash,
  FaSearch,
  FaCreditCard,
  FaArrowLeft,
  FaSync,
  FaFilter,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';
import { useAuthStore } from '../stores/authStore';
import Modal from '../components/Modal';
import SimpleConfirmModal from '../components/SimpleConfirmModal';
import SimpleSuccessModal from '../components/SimpleSuccessModal';
import ToggleSwitch from '../components/ToggleSwitch';
import SelectBox from '../components/SelectBox';

interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
}

interface Debt {
  id: number;
  customer_id: number;
  sale_id?: number;
  amount: number;
  remaining_amount: number;
  description?: string;
  is_paid: boolean;
  created_at: string;
  updated_at?: string;
  customer?: Customer;
  payments: Payment[];
}

interface Payment {
  id: number;
  debt_id: number;
  amount: number;
  payment_method: string;
  notes?: string;
  created_at: string;
}

interface DebtFormData {
  customer_id: number;
  amount: number;
  description: string;
}

interface PaymentFormData {
  amount: number;
  payment_method: string;
  notes: string;
}

const Debts: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [searchParams] = useSearchParams();
  const customerId = searchParams.get('customer_id');

  const [debts, setDebts] = useState<Debt[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showUnpaidOnly, setShowUnpaidOnly] = useState(true);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(
    customerId ? parseInt(customerId) : null
  );

  // Modals
  const [showDebtModal, setShowDebtModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Form data
  const [editingDebt, setEditingDebt] = useState<Debt | null>(null);
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  const [debtToDelete, setDebtToDelete] = useState<Debt | null>(null);
  const [debtFormData, setDebtFormData] = useState<DebtFormData>({
    customer_id: 0,
    amount: 0,
    description: ''
  });
  const [paymentFormData, setPaymentFormData] = useState<PaymentFormData>({
    amount: 0,
    payment_method: 'cash',
    notes: ''
  });
  const [formErrors, setFormErrors] = useState<any>({});

  // New state variables for unified design
  const [showFilters, setShowFilters] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1
  });
  const [stats, setStats] = useState({
    totalDebts: 0,
    unpaidDebts: 0,
    totalAmount: 0,
    unpaidAmount: 0
  });

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      window.location.href = '/login';
      return;
    }
    fetchCustomers();
    fetchDebts();
    fetchStats();
  }, [user]); // Removed selectedCustomerId and showUnpaidOnly from dependencies

  const fetchStats = async () => {
    try {
      const { token } = useAuthStore.getState();
      const response = await fetch('http://localhost:8002/api/debts/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      // Handle error silently
      setStats({
        totalDebts: 0,
        unpaidDebts: 0,
        totalAmount: 0,
        unpaidAmount: 0
      });
    }
  };

  const fetchCustomers = async () => {
    try {
      const { token } = useAuthStore.getState();
      const response = await fetch('http://localhost:8002/api/customers?active_only=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCustomers(data);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const fetchDebts = async (page = currentPage, limit = itemsPerPage) => {
    try {
      setLoading(true);
      const { token } = useAuthStore.getState();
      let url = `http://localhost:8002/api/debts?unpaid_only=${showUnpaidOnly}&skip=${(page - 1) * limit}&limit=${limit}`;

      if (selectedCustomerId) {
        url += `&customer_id=${selectedCustomerId}`;
      }

      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();

        // Handle pagination - if backend doesn't support it, do client-side pagination
        let allDebts = data;
        let paginationInfo;

        if (response.headers.get('x-total-count')) {
          // Backend supports pagination
          const total = parseInt(response.headers.get('x-total-count') || '0');
          paginationInfo = {
            total: total,
            page: page,
            limit: limit,
            pages: Math.ceil(total / limit)
          };
          setDebts(allDebts);
        } else {
          // Client-side pagination
          const total = allDebts.length;
          paginationInfo = {
            total: total,
            page: page,
            limit: limit,
            pages: Math.ceil(total / limit)
          };
          setDebts(allDebts);
        }

        setPagination(paginationInfo);
        setCurrentPage(page);
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchDebts(1, itemsPerPage);
  };

  const handleApplyFilters = () => {
    setCurrentPage(1); // Reset to first page when applying filters
    fetchDebts(1, itemsPerPage);
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setSelectedCustomerId(null);
    setShowUnpaidOnly(true);
    setItemsPerPage(10);
    setCurrentPage(1);
    // Apply reset immediately
    fetchDebts(1, 10);
  };

  const handleAddDebt = () => {
    setEditingDebt(null);
    setDebtFormData({
      customer_id: selectedCustomerId || 0,
      amount: 0,
      description: ''
    });
    setFormErrors({});
    setShowDebtModal(true);
  };

  const handleEditDebt = (debt: Debt) => {
    setEditingDebt(debt);
    setDebtFormData({
      customer_id: debt.customer_id,
      amount: debt.amount,
      description: debt.description || ''
    });
    setFormErrors({});
    setShowDebtModal(true);
  };

  const handleAddPayment = (debt: Debt) => {
    setSelectedDebt(debt);
    setPaymentFormData({
      amount: debt.remaining_amount,
      payment_method: 'cash',
      notes: ''
    });
    setFormErrors({});
    setShowPaymentModal(true);
  };

  const handleDeleteDebt = (debt: Debt) => {
    setDebtToDelete(debt);
    setShowDeleteModal(true);
  };

  const validateDebtForm = (): boolean => {
    const errors: any = {};

    if (!debtFormData.customer_id) {
      errors.customer_id = 'يجب اختيار العميل';
    }

    if (!debtFormData.amount || debtFormData.amount <= 0) {
      errors.amount = 'يجب إدخال مبلغ صحيح';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validatePaymentForm = (): boolean => {
    const errors: any = {};

    if (!paymentFormData.amount || paymentFormData.amount <= 0) {
      errors.amount = 'يجب إدخال مبلغ صحيح';
    }

    if (selectedDebt && paymentFormData.amount > selectedDebt.remaining_amount) {
      errors.amount = 'المبلغ أكبر من المبلغ المتبقي';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleDebtSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateDebtForm()) {
      return;
    }

    try {
      const { token } = useAuthStore.getState();
      const url = editingDebt
        ? `http://localhost:8002/api/debts/${editingDebt.id}`
        : 'http://localhost:8002/api/debts';

      const method = editingDebt ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(debtFormData),
      });

      if (response.ok) {
        setShowDebtModal(false);
        setSuccessMessage(editingDebt ? 'تم تحديث المديونية بنجاح' : 'تم إضافة المديونية بنجاح');
        setShowSuccessModal(true);
        fetchDebts();
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePaymentForm() || !selectedDebt) {
      return;
    }

    try {
      const { token } = useAuthStore.getState();
      const response = await fetch(`http://localhost:8002/api/debts/${selectedDebt.id}/payments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          debt_id: selectedDebt.id,
          ...paymentFormData
        }),
      });

      if (response.ok) {
        setShowPaymentModal(false);
        setSuccessMessage('تم تسجيل الدفعة بنجاح');
        setShowSuccessModal(true);
        fetchDebts();
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const confirmDelete = async () => {
    if (!debtToDelete) return;

    try {
      const { token } = useAuthStore.getState();
      const response = await fetch(`http://localhost:8002/api/debts/${debtToDelete.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setShowDeleteModal(false);
        setSuccessMessage('تم حذف المديونية بنجاح');
        setShowSuccessModal(true);
        fetchDebts();
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const filteredDebts = debts.filter(debt => {
    const customerName = debt.customer?.name || '';
    return customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           debt.description?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm"
              >
                <FaArrowLeft />
              </button>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center">
                <FaMoneyBillWave className="ml-3 text-primary-600 dark:text-primary-400" />
                إدارة المديونية
              </h1>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              <button
                onClick={() => fetchDebts(currentPage, itemsPerPage)}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2 rounded-md hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm"
                title="تحديث البيانات"
              >
                <FaSync className={loading ? 'animate-spin' : ''} />
              </button>
              <button
                onClick={handleAddDebt}
                className="bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg"
              >
                <FaPlus className="ml-2" />
                <span className="hidden sm:inline">إضافة مديونية جديدة</span>
                <span className="sm:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Debts */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المديونيات</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{stats.totalDebts}</div>
            </div>
          </div>

          {/* Unpaid Debts */}
          <div className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30">
            <div className="bg-red-100 dark:bg-red-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaTimes className="text-red-600 dark:text-red-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-red-700 dark:text-red-300 mb-1">غير مدفوعة</div>
              <div className="text-xl font-bold text-red-600 dark:text-red-400">{stats.unpaidDebts}</div>
            </div>
          </div>

          {/* Total Amount */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-blue-600 dark:text-blue-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">إجمالي المبلغ</div>
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">
                {stats.totalAmount.toFixed(2)} د.ل
              </div>
              <div className="text-xs text-blue-500 dark:text-blue-400">المبلغ الكلي</div>
            </div>
          </div>

          {/* Unpaid Amount */}
          <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-100 dark:border-orange-800/30">
            <div className="bg-orange-100 dark:bg-orange-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-orange-600 dark:text-orange-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">المبلغ غير المدفوع</div>
              <div className="text-lg font-bold text-orange-600 dark:text-orange-400 truncate">
                {stats.unpaidAmount.toFixed(2)} د.ل
              </div>
              <div className="text-xs text-orange-500 dark:text-orange-400">المبلغ المستحق</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث بالعميل أو الوصف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={handleSearch}
              className="btn-primary flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 xl:grid-cols-6 gap-6">
              {/* Customer Filter */}
              <div className="sm:col-span-1 lg:col-span-2 xl:col-span-2">
                <SelectBox
                  label="العميل"
                  name="customer"
                  value={selectedCustomerId?.toString() || ''}
                  onChange={(value) => setSelectedCustomerId(value ? parseInt(value) : null)}
                  options={[
                    { value: '', label: 'جميع العملاء' },
                    ...customers.map(customer => ({
                      value: customer.id.toString(),
                      label: customer.name
                    }))
                  ]}
                />
              </div>

              {/* Payment Status Filter */}
              <div className="sm:col-span-1 lg:col-span-1 xl:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  حالة الدفع
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center justify-start">
                  <ToggleSwitch
                    id="unpaidOnly"
                    checked={showUnpaidOnly}
                    onChange={(checked) => setShowUnpaidOnly(checked)}
                    label="غير المدفوعة فقط"
                  />
                </div>
              </div>

              {/* Items Per Page Filter */}
              <div className="sm:col-span-1 lg:col-span-1 xl:col-span-1">
                <SelectBox
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value) => setItemsPerPage(parseInt(value, 10))}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                />
              </div>

              {/* Action Buttons */}
              <div className="sm:col-span-2 lg:col-span-1 xl:col-span-1 flex flex-col gap-3">
                <button
                  type="button"
                  onClick={handleResetFilters}
                  className="bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2.5 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm font-medium"
                >
                  إعادة تعيين
                </button>
                <button
                  type="button"
                  onClick={handleApplyFilters}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2.5 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm font-medium"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Debts List */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700">
        {filteredDebts.length === 0 ? (
          <div className="text-center py-12">
            <FaMoneyBillWave className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">لا توجد مديونيات</p>
          </div>
        ) : (
          <div className="overflow-x-auto custom-scrollbar-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ الإجمالي
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المتبقي
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredDebts.map((debt) => (
                  <tr key={debt.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FaUser className="text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {debt.customer?.name || 'عميل غير معروف'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            #{debt.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaMoneyBillWave className="text-blue-500 ml-2" />
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {debt.amount.toFixed(2)} د.ل
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaMoneyBillWave className={`ml-2 ${debt.remaining_amount > 0 ? 'text-red-500' : 'text-green-500'}`} />
                        <span className={`text-sm font-medium ${debt.remaining_amount > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                          {debt.remaining_amount.toFixed(2)} د.ل
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {debt.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaCalendarAlt className="text-gray-400 ml-2" />
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {new Date(debt.created_at).toLocaleDateString('ar-LY')}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        debt.is_paid
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {debt.is_paid ? (
                          <>
                            <FaCheck className="ml-1" />
                            مدفوعة
                          </>
                        ) : (
                          <>
                            <FaTimes className="ml-1" />
                            غير مدفوعة
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        {!debt.is_paid && (
                          <button
                            onClick={() => handleAddPayment(debt)}
                            className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                            title="إضافة دفعة"
                          >
                            <FaCreditCard />
                          </button>
                        )}
                        <button
                          onClick={() => handleEditDebt(debt)}
                          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                          title="تعديل"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDeleteDebt(debt)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                          title="حذف"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {!loading && filteredDebts.length > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => {
                  const newPage = Math.max(1, pagination.page - 1);
                  setCurrentPage(newPage);
                  fetchDebts(newPage, itemsPerPage);
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page <= 1
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                السابق
              </button>
              <button
                onClick={() => {
                  const newPage = Math.min(pagination.pages, pagination.page + 1);
                  setCurrentPage(newPage);
                  fetchDebts(newPage, itemsPerPage);
                }}
                disabled={pagination.page >= pagination.pages}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.pages
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                  <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                  <button
                    onClick={() => {
                      const newPage = Math.max(1, pagination.page - 1);
                      setCurrentPage(newPage);
                      fetchDebts(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">السابق</span>
                    <FaChevronRight className="h-5 w-5" />
                  </button>

                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    let pageNum;
                    if (pagination.pages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          setCurrentPage(pageNum);
                          fetchDebts(pageNum, itemsPerPage);
                        }}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => {
                      const newPage = Math.min(pagination.pages, pagination.page + 1);
                      setCurrentPage(newPage);
                      fetchDebts(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">التالي</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Debt Form Modal */}
      <Modal
        isOpen={showDebtModal}
        onClose={() => setShowDebtModal(false)}
        title={editingDebt ? 'تعديل المديونية' : 'إضافة مديونية جديدة'}
      >
        <form onSubmit={handleDebtSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              العميل *
            </label>
            <select
              value={debtFormData.customer_id}
              onChange={(e) => setDebtFormData({ ...debtFormData, customer_id: parseInt(e.target.value) })}
              className={`input-field ${formErrors.customer_id ? 'border-red-500' : ''}`}
              disabled={!!editingDebt}
            >
              <option value={0}>اختر العميل</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
            {formErrors.customer_id && (
              <p className="text-red-500 text-sm mt-1">{formErrors.customer_id}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المبلغ *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={debtFormData.amount}
              onChange={(e) => setDebtFormData({ ...debtFormData, amount: parseFloat(e.target.value) || 0 })}
              className={`input-field ${formErrors.amount ? 'border-red-500' : ''}`}
              placeholder="أدخل المبلغ"
            />
            {formErrors.amount && (
              <p className="text-red-500 text-sm mt-1">{formErrors.amount}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الوصف
            </label>
            <textarea
              value={debtFormData.description}
              onChange={(e) => setDebtFormData({ ...debtFormData, description: e.target.value })}
              className="input-field"
              placeholder="أدخل وصف المديونية"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setShowDebtModal(false)}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
            >
              {editingDebt ? 'تحديث' : 'إضافة'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Payment Form Modal */}
      <Modal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        title="إضافة دفعة"
      >
        <form onSubmit={handlePaymentSubmit} className="space-y-4">
          {selectedDebt && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                تفاصيل المديونية
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">العميل:</span>
                  <span className="mr-2 font-medium">{selectedDebt.customer?.name}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">المبلغ الإجمالي:</span>
                  <span className="mr-2 font-medium">{selectedDebt.amount.toFixed(2)} د.ل</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">المبلغ المتبقي:</span>
                  <span className="mr-2 font-medium text-red-600">{selectedDebt.remaining_amount.toFixed(2)} د.ل</span>
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              مبلغ الدفعة *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              max={selectedDebt?.remaining_amount}
              value={paymentFormData.amount}
              onChange={(e) => setPaymentFormData({ ...paymentFormData, amount: parseFloat(e.target.value) || 0 })}
              className={`input-field ${formErrors.amount ? 'border-red-500' : ''}`}
              placeholder="أدخل مبلغ الدفعة"
            />
            {formErrors.amount && (
              <p className="text-red-500 text-sm mt-1">{formErrors.amount}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              طريقة الدفع
            </label>
            <select
              value={paymentFormData.payment_method}
              onChange={(e) => setPaymentFormData({ ...paymentFormData, payment_method: e.target.value })}
              className="input-field"
            >
              <option value="cash">نقدي</option>
              <option value="card">بطاقة</option>
              <option value="bank_transfer">تحويل بنكي</option>
              <option value="check">شيك</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              ملاحظات
            </label>
            <textarea
              value={paymentFormData.notes}
              onChange={(e) => setPaymentFormData({ ...paymentFormData, notes: e.target.value })}
              className="input-field"
              placeholder="أدخل ملاحظات إضافية"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setShowPaymentModal(false)}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
            >
              تسجيل الدفعة
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <SimpleConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        title="تأكيد الحذف"
        message={`هل أنت متأكد من حذف هذه المديونية؟`}
        confirmText="حذف"
        cancelText="إلغاء"
      />

      {/* Success Modal */}
      <SimpleSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        message={successMessage}
      />
    </div>
  );
};

export default Debts;
